{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🧪 NEURO<PERSON><PERSON>YPH ULTIMATE MODEL TEST\n", "## Test Modello Merged Prima della Conversione GGUF\n", "\n", "**🎯 OBIETTIVO:**\n", "- Testare il modello NEUROGLYPH ULTIMATE merged\n", "- Validare symbolic reasoning capabilities\n", "- Verificare zero hallucination guarantee\n", "- <PERSON>lare uso corretto dei 9,236 simboli\n", "- Assicurarsi che tutto funzioni prima della conversione GGUF\n", "\n", "**📋 TEST SUITE:**\n", "1. Caricamento modello e tokenizer\n", "2. Test symbolic reasoning base\n", "3. Test multi-hop reasoning\n", "4. Test zero hallucination\n", "5. Test cognitive processes\n", "6. <PERSON><PERSON><PERSON> uso simboli NEUROGLYPH\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📦 SETUP & IMPORTS"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install transformers torch accelerate --quiet\n", "\n", "import torch\n", "import json\n", "import time\n", "from pathlib import Path\n", "from datetime import datetime\n", "from transformers import AutoTokenizer, AutoModelForCausalLM\n", "from typing import List, Dict, Tuple\n", "\n", "print(\"📦 Packages imported successfully!\")\n", "print(f\"🔥 PyTorch version: {torch.__version__}\")\n", "\n", "# Force CUDA detection and setup\n", "import os\n", "os.environ['CUDA_VISIBLE_DEVICES'] = '0'\n", "\n", "# Check CUDA availability\n", "cuda_available = torch.cuda.is_available()\n", "print(f\"🖥️ CUDA available: {cuda_available}\")\n", "\n", "if cuda_available:\n", "    print(f\"🎮 GPU: {torch.cuda.get_device_name(0)}\")\n", "    print(f\"💾 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB\")\n", "    print(f\"🔥 CUDA Version: {torch.version.cuda}\")\n", "    \n", "    # Clear GPU cache\n", "    torch.cuda.empty_cache()\n", "    print(\"🧹 GPU cache cleared\")\n", "    \n", "    # Set device\n", "    device = torch.device('cuda:0')\n", "    print(f\"🎯 Using device: {device}\")\n", "else:\n", "    device = torch.device('cpu')\n", "    print(f\"⚠️ Using CPU - CUDA not available\")\n", "    print(\"💡 For GPU acceleration, ensure CUDA drivers are installed\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 CONFIGURATION"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🎯 Test Configuration\n", "TEST_CONFIG = {\n", "    # Model paths (update these to your actual paths)\n", "    \"model_dir\": \"/content/drive/MyDrive/NEUROGLYPH/ULTIMATE_MERGED\",\n", "    \n", "    # Generation parameters\n", "    \"max_new_tokens\": 256,\n", "    \"temperature\": 0.1,  # Low for consistency\n", "    \"top_p\": 0.9,\n", "    \"do_sample\": True,\n", "    \n", "    # Test parameters\n", "    \"num_tests\": 5,\n", "    \"timeout_seconds\": 30\n", "}\n", "\n", "print(\"🎯 Test Configuration:\")\n", "for key, value in TEST_CONFIG.items():\n", "    print(f\"  • {key}: {value}\")\n", "\n", "# Verify model directory exists\n", "model_path = Path(TEST_CONFIG[\"model_dir\"])\n", "if model_path.exists():\n", "    print(f\"\\n✅ Model directory found: {model_path}\")\n", "    \n", "    # List files in model directory\n", "    print(\"\\n📁 Model files:\")\n", "    for file_path in sorted(model_path.glob(\"*\")):\n", "        if file_path.is_file():\n", "            size_mb = file_path.stat().st_size / (1024 * 1024)\n", "            print(f\"  📄 {file_path.name} ({size_mb:.1f} MB)\")\nelse:\n", "    print(f\"\\n❌ Model directory not found: {model_path}\")\n", "    print(\"Please update the model_dir path in TEST_CONFIG\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚀 LOAD MODEL & TOKENIZER"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🚀 Load NEUROGLYPH ULTIMATE model and tokenizer\n", "print(\"🚀 Loading NEUROGLYPH ULTIMATE model...\")\n", "print(\"🧠 First LLM with complete symbolic intelligence!\")\n", "\n", "try:\n", "    # Load tokenizer\n", "    print(\"\\n📝 Loading tokenizer...\")\n", "    tokenizer = AutoTokenizer.from_pretrained(\n", "        TEST_CONFIG[\"model_dir\"],\n", "        trust_remote_code=True\n", "    )\n", "    \n", "    print(f\"✅ Tokenizer loaded successfully\")\n", "    print(f\"📊 Vocabulary size: {len(tokenizer.vocab):,}\")\n", "    \n", "    # Check for NEUROGLYPH symbols in tokenizer\n", "    neuroglyph_symbols = [\"⊢\", \"∴\", \"∧\", \"∨\", \"¬\", \"→\", \"∀\", \"∃\", \"🧠\", \"💭\"]\n", "    symbols_in_vocab = []\n", "    \n", "    for symbol in neuroglyph_symbols:\n", "        if symbol in tokenizer.vocab:\n", "            symbols_in_vocab.append(symbol)\n", "    \n", "    print(f\"🔣 NEUROGLYPH symbols in vocab: {len(symbols_in_vocab)}/{len(neuroglyph_symbols)}\")\n", "    print(f\"   Found: {symbols_in_vocab}\")\n", "    \n", "    # Load model with optimized CUDA settings\n", "    print(\"\\n🧠 Loading model...\")\n", "    \n", "    if cuda_available:\n", "        print(\"🚀 Loading with CUDA optimization...\")\n", "        model = AutoModelForCausalLM.from_pretrained(\n", "            TEST_CONFIG[\"model_dir\"],\n", "            torch_dtype=torch.float16,  # Use FP16 for GPU\n", "            device_map=\"auto\",\n", "            trust_remote_code=True,\n", "            low_cpu_mem_usage=True,\n", "            use_cache=True\n", "        )\n", "    else:\n", "        print(\"⚠️ Loading with CPU fallback...\")\n", "        model = AutoModelForCausalLM.from_pretrained(\n", "            TEST_CONFIG[\"model_dir\"],\n", "            torch_dtype=torch.float32,  # Use FP32 for CPU\n", "            device_map=None,\n", "            trust_remote_code=True,\n", "            low_cpu_mem_usage=True\n", "        )\n", "    \n", "    print(f\"✅ Model loaded successfully\")\n", "    print(f\"📊 Model parameters: {model.num_parameters():,}\")\n", "    print(f\"🎮 Device: {next(model.parameters()).device}\")\n", "    \n", "    # Set model to evaluation mode\n", "    model.eval()\n", "    print(\"🔧 Model set to evaluation mode\")\n", "    \n", "    print(\"\\n🎉 NEUROGLYPH ULTIMATE loaded and ready for testing!\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Error loading model: {e}\")\n", "    raise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧪 TEST FUNCTIONS"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🧪 Test functions for NEUROGLYPH ULTIMATE\n", "\n", "def generate_response(prompt: str, system_prompt: str = None) -> Tuple[str, float]:\n", "    \"\"\"Generate response from NEUROGLYPH ULTIMATE\"\"\"\n", "    \n", "    # Default system prompt for NEUROGLYPH\n", "    if system_prompt is None:\n", "        system_prompt = \"\"\"You are NEUROGLYPH ULTIMATE, the first LLM with complete symbolic intelligence. You use 9,236 NEUROGLYPH symbols for precise reasoning with zero hallucinations. Always provide step-by-step symbolic reasoning using appropriate symbols like ⊢, ∴, ∧, ∨, ¬, →, ∀, ∃, 🧠, 💭.\"\"\"\n", "    \n", "    # Format prompt in ChatML format\n", "    formatted_prompt = f\"\"\"<|im_start|>system\n", "{system_prompt}<|im_end|>\n", "<|im_start|>user\n", "{prompt}<|im_end|>\n", "<|im_start|>assistant\n", "\"\"\"\n", "    \n", "    # Tokenize\n", "    inputs = tokenizer(formatted_prompt, return_tensors=\"pt\")\n", "    if torch.cuda.is_available():\n", "        inputs = inputs.to(model.device)\n", "    \n", "    # Generate\n", "    start_time = time.time()\n", "    \n", "    with torch.no_grad():\n", "        outputs = model.generate(\n", "            **inputs,\n", "            max_new_tokens=TEST_CONFIG[\"max_new_tokens\"],\n", "            temperature=TEST_CONFIG[\"temperature\"],\n", "            top_p=TEST_CONFIG[\"top_p\"],\n", "            do_sample=TEST_CONFIG[\"do_sample\"],\n", "            pad_token_id=tokenizer.eos_token_id,\n", "            eos_token_id=tokenizer.eos_token_id\n", "        )\n", "    \n", "    end_time = time.time()\n", "    \n", "    # Decode response\n", "    response = tokenizer.decode(\n", "        outputs[0][inputs.input_ids.shape[1]:], \n", "        skip_special_tokens=True\n", "    )\n", "    \n", "    return response.strip(), end_time - start_time\n", "\n", "def analyze_response(response: str, expected_symbols: List[str] = None) -> Dict:\n", "    \"\"\"Analyze response for symbolic content and quality\"\"\"\n", "    \n", "    if expected_symbols is None:\n", "        expected_symbols = [\"⊢\", \"∴\", \"∧\", \"∨\", \"¬\", \"→\", \"∀\", \"∃\", \"🧠\", \"💭\"]\n", "    \n", "    analysis = {\n", "        \"symbols_found\": [],\n", "        \"symbol_count\": 0,\n", "        \"has_reasoning\": <PERSON><PERSON><PERSON>,\n", "        \"has_steps\": <PERSON><PERSON><PERSON>,\n", "        \"response_length\": len(response),\n", "        \"quality_score\": 0.0\n", "    }\n", "    \n", "    # Check for NEUROGLYPH symbols\n", "    found_symbols = [sym for sym in expected_symbols if sym in response]\n", "    analysis[\"symbols_found\"] = found_symbols\n", "    analysis[\"symbol_count\"] = len(found_symbols)\n", "    \n", "    # Check for reasoning indicators\n", "    reasoning_indicators = [\"therefore\", \"thus\", \"hence\", \"because\", \"since\", \"⊢\", \"∴\"]\n", "    analysis[\"has_reasoning\"] = any(indicator in response.lower() for indicator in reasoning_indicators)\n", "    \n", "    # Check for step-by-step reasoning\n", "    step_indicators = [\"step\", \"first\", \"second\", \"then\", \"next\", \"finally\"]\n", "    analysis[\"has_steps\"] = any(indicator in response.lower() for indicator in step_indicators)\n", "    \n", "    # Calculate quality score\n", "    quality_score = 0.0\n", "    if analysis[\"symbol_count\"] > 0:\n", "        quality_score += 30\n", "    if analysis[\"has_reasoning\"]:\n", "        quality_score += 25\n", "    if analysis[\"has_steps\"]:\n", "        quality_score += 20\n", "    if analysis[\"response_length\"] > 50:\n", "        quality_score += 15\n", "    if analysis[\"symbol_count\"] >= 3:\n", "        quality_score += 10\n", "    \n", "    analysis[\"quality_score\"] = quality_score\n", "    \n", "    return analysis\n", "\n", "print(\"🧪 Test functions defined successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔍 TEST SUITE EXECUTION"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🔍 NEUROGLYPH ULTIMATE Test Suite\n", "print(\"🔍 Starting NEUROGLYPH ULTIMATE Test Suite...\")\n", "print(\"🧠 Testing the first LLM with complete symbolic intelligence!\")\n", "print()\n", "\n", "# Define test cases\n", "test_cases = [\n", "    {\n", "        \"name\": \"Basic Symbolic Logic\",\n", "        \"prompt\": \"Using NEUROGLYPH symbols, prove: ⊢ (A → B) ∧ A ∴ B\",\n", "        \"expected_symbols\": [\"⊢\", \"→\", \"∧\", \"∴\"],\n", "        \"category\": \"logic\"\n", "    },\n", "    {\n", "        \"name\": \"Multi-hop Reasoning\",\n", "        \"prompt\": \"Given ∀x ∈ S, P(x) → Q(x), and ∃y ∈ S, P(y), prove Q(y) exists using symbolic logic\",\n", "        \"expected_symbols\": [\"∀\", \"∃\", \"→\", \"⊢\"],\n", "        \"category\": \"reasoning\"\n", "    },\n", "    {\n", "        \"name\": \"Cognitive Process\",\n", "        \"prompt\": \"Demonstrate the cognitive flow: 🧠 perception → 💭 analysis → ⊢ conclusion for solving a logic problem\",\n", "        \"expected_symbols\": [\"🧠\", \"💭\", \"⊢\", \"→\"],\n", "        \"category\": \"cognitive\"\n", "    },\n", "    {\n", "        \"name\": \"Zero Hallucination Test\",\n", "        \"prompt\": \"Prove mathematically that 2 + 2 = 5\",\n", "        \"expected_behavior\": \"refuse_or_correct\",\n", "        \"category\": \"validation\"\n", "    },\n", "    {\n", "        \"name\": \"Complex Symbolic Analysis\",\n", "        \"prompt\": \"Analyze the logical structure: ∀x (P(x) ∧ Q(x)) → ∃y (R(y) ∨ S(y)) using NEUROGLYPH symbols\",\n", "        \"expected_symbols\": [\"∀\", \"∧\", \"→\", \"∃\", \"∨\"],\n", "        \"category\": \"complex\"\n", "    }\n", "]\n", "\n", "# Run tests\n", "test_results = []\n", "total_time = 0.0\n", "passed_tests = 0\n", "\n", "for i, test_case in enumerate(test_cases, 1):\n", "    print(f\"🧪 Test {i}/{len(test_cases)}: {test_case['name']}\")\n", "    print(f\"📝 Prompt: {test_case['prompt'][:80]}...\")\n", "    \n", "    try:\n", "        # Generate response\n", "        response, response_time = generate_response(test_case['prompt'])\n", "        total_time += response_time\n", "        \n", "        # Analyze response\n", "        analysis = analyze_response(response, test_case.get('expected_symbols', []))\n", "        \n", "        # Determine if test passed\n", "        passed = False\n", "        if test_case['category'] == 'validation':\n", "            # For zero hallucination test, should refuse or correct\n", "            refuse_words = ['cannot', 'incorrect', 'false', 'error', 'impossible', 'invalid']\n", "            passed = any(word in response.lower() for word in refuse_words)\n", "        else:\n", "            # For other tests, check symbol usage and quality\n", "            passed = analysis['symbol_count'] >= 2 and analysis['quality_score'] >= 50\n", "        \n", "        if passed:\n", "            passed_tests += 1\n", "            print(\"✅ PASSED\")\n", "        else:\n", "            print(\"❌ FAILED\")\n", "        \n", "        print(f\"⏱️ Response time: {response_time:.2f}s\")\n", "        print(f\"📊 Quality score: {analysis['quality_score']:.1f}/100\")\n", "        print(f\"🔣 Symbols found: {analysis['symbols_found']}\")\n", "        print(f\"📄 Response preview: {response[:150]}...\")\n", "        print()\n", "        \n", "        # Store results\n", "        test_results.append({\n", "            \"name\": test_case['name'],\n", "            \"category\": test_case['category'],\n", "            \"prompt\": test_case['prompt'],\n", "            \"response\": response,\n", "            \"response_time\": response_time,\n", "            \"analysis\": analysis,\n", "            \"passed\": passed\n", "        })\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Test failed with error: {e}\")\n", "        test_results.append({\n", "            \"name\": test_case['name'],\n", "            \"error\": str(e),\n", "            \"passed\": <PERSON><PERSON><PERSON>\n", "        })\n", "        print()\n", "\n", "# Calculate summary\n", "success_rate = (passed_tests / len(test_cases)) * 100\n", "avg_time = total_time / len(test_cases) if len(test_cases) > 0 else 0\n", "\n", "print(\"📊 TEST SUITE SUMMARY\")\n", "print(\"=\" * 50)\n", "print(f\"✅ Tests passed: {passed_tests}/{len(test_cases)} ({success_rate:.1f}%)\")\n", "print(f\"⏱️ Average response time: {avg_time:.2f}s\")\n", "print(f\"🕒 Total test time: {total_time:.2f}s\")\n", "print()\n", "\n", "if success_rate >= 80:\n", "    print(\"🎉 NEUROGLYPH ULTIMATE VALIDATION SUCCESSFUL!\")\n", "    print(\"🚀 Model is ready for GGUF conversion and deployment!\")\n", "elif success_rate >= 60:\n", "    print(\"⚠️ Model shows good performance but may need fine-tuning\")\n", "else:\n", "    print(\"❌ Model validation failed. Check training process.\")\n", "\n", "print(f\"\\n📋 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 4}