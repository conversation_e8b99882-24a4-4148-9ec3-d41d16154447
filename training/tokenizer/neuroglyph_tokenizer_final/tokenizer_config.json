{"added_tokens_decoder": {"0": {"content": "[UNK]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "1": {"content": "[PAD]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "2": {"content": "[CLS]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "3": {"content": "[SEP]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "4": {"content": "[MASK]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}}, "clean_up_tokenization_spaces": false, "cls_token": "[CLS]", "extra_special_tokens": {}, "mask_token": "[MASK]", "model_max_length": 1000000000000000019884624838656, "pad_token": "[PAD]", "sep_token": "[SEP]", "tokenizer_class": "PreTrainedTokenizer", "unk_token": "[UNK]"}