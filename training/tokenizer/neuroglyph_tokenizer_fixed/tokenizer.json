{"version": "1.0", "truncation": null, "padding": null, "added_tokens": [{"id": 0, "content": "<UNK>", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"id": 1, "content": "<PAD>", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"id": 2, "content": "<BOS>", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"id": 3, "content": "<EOS>", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"id": 4, "content": "<MASK>", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"id": 5, "content": "<NG_START>", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"id": 6, "content": "<NG_END>", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"id": 7, "content": "<NG_THINK>", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"id": 8, "content": "<NG_REASON>", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"id": 9, "content": "<NG_MEMORY>", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"id": 10, "content": "<NG_VALIDATE>", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"id": 11, "content": "<NG_ERROR>", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"id": 12, "content": "<NG_CORRECT>", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false, "special": true}], "normalizer": {"type": "Sequence", "normalizers": [{"type": "NFD"}, {"type": "StripAccents"}]}, "pre_tokenizer": {"type": "Sequence", "pretokenizers": [{"type": "Whitespace"}, {"type": "Punctuation", "behavior": "Isolated"}]}, "post_processor": {"type": "TemplateProcessing", "single": [{"SpecialToken": {"id": "<BOS>", "type_id": 0}}, {"Sequence": {"id": "A", "type_id": 0}}, {"SpecialToken": {"id": "<EOS>", "type_id": 0}}], "pair": [{"SpecialToken": {"id": "<BOS>", "type_id": 0}}, {"Sequence": {"id": "A", "type_id": 0}}, {"SpecialToken": {"id": "<EOS>", "type_id": 0}}, {"Sequence": {"id": "B", "type_id": 1}}, {"SpecialToken": {"id": "<EOS>", "type_id": 1}}], "special_tokens": {"<BOS>": {"id": "<BOS>", "ids": [2], "tokens": ["<BOS>"]}, "<EOS>": {"id": "<EOS>", "ids": [3], "tokens": ["<EOS>"]}}}, "decoder": null, "model": {"type": "BPE", "dropout": null, "unk_token": "<UNK>", "continuing_subword_prefix": "##", "end_of_word_suffix": null, "fuse_unk": false, "byte_fallback": false, "ignore_merges": false, "vocab": {"<UNK>": 0, "<PAD>": 1, "<BOS>": 2, "<EOS>": 3, "<MASK>": 4, "<NG_START>": 5, "<NG_END>": 6, "<NG_THINK>": 7, "<NG_REASON>": 8, "<NG_MEMORY>": 9, "<NG_VALIDATE>": 10, "<NG_ERROR>": 11, "<NG_CORRECT>": 12, "#": 13, "(": 14, ")": 15, ",": 16, "-": 17, "0": 18, "1": 19, "2": 20, "3": 21, "4": 22, "5": 23, "6": 24, "7": 25, "8": 26, "9": 27, ":": 28, ";": 29, "<": 30, "=": 31, ">": 32, "A": 33, "B": 34, "C": 35, "D": 36, "E": 37, "F": 38, "G": 39, "I": 40, "N": 41, "P": 42, "R": 43, "S": 44, "T": 45, "W": 46, "[": 47, "]": 48, "_": 49, "a": 50, "b": 51, "c": 52, "d": 53, "e": 54, "f": 55, "g": 56, "h": 57, "i": 58, "j": 59, "k": 60, "l": 61, "m": 62, "n": 63, "o": 64, "p": 65, "q": 66, "r": 67, "s": 68, "t": 69, "u": 70, "v": 71, "w": 72, "x": 73, "y": 74, "z": 75, "{": 76, "}": 77, "Ω": 78, "‐": 79, "‑": 80, "‒": 81, "–": 82, "—": 83, "―": 84, "‖": 85, "‗": 86, "‘": 87, "”": 88, "„": 89, "•": 90, "‧": 91, "‴": 92, "‷": 93, "⁅": 94, "⁆": 95, "⁎": 96, "⁏": 97, "⁵": 98, "⁸": 99, "⁺": 100, "ⁿ": 101, "₀": 102, "ₐ": 103, "ₒ": 104, "ₓ": 105, "ₔ": 106, "ₛ": 107, "₠": 108, "₣": 109, "₦": 110, "₪": 111, "₭": 112, "₮": 113, "₱": 114, "₵": 115, "₸": 116, "₼": 117, "℀": 118, "℁": 119, "ℂ": 120, "℃": 121, "℆": 122, "℈": 123, "ℌ": 124, "ℎ": 125, "ℏ": 126, "ℐ": 127, "ℓ": 128, "℔": 129, "ℕ": 130, "№": 131, "ℙ": 132, "ℛ": 133, "ℜ": 134, "ℝ": 135, "℞": 136, "℠": 137, "℡": 138, "ℤ": 139, "℥": 140, "ℬ": 141, "ℭ": 142, "℮": 143, "ℯ": 144, "ℱ": 145, "ℳ": 146, "ℵ": 147, "ℶ": 148, "ℷ": 149, "ℸ": 150, "℺": 151, "℻": 152, "ℽ": 153, "ℿ": 154, "⅀": 155, "ⅅ": 156, "ⅆ": 157, "ⅇ": 158, "ⅉ": 159, "⅌": 160, "⅍": 161, "ⅎ": 162, "⅏": 163, "⅐": 164, "⅑": 165, "⅖": 166, "⅘": 167, "⅙": 168, "⅛": 169, "⅝": 170, "Ⅳ": 171, "Ⅴ": 172, "Ⅶ": 173, "Ⅹ": 174, "Ⅺ": 175, "ⅰ": 176, "ⅴ": 177, "ⅷ": 178, "ⅸ": 179, "ⅻ": 180, "ⅼ": 181, "ⅽ": 182, "ⅿ": 183, "ↀ": 184, "ↇ": 185, "←": 186, "↑": 187, "→": 188, "↔": 189, "↕": 190, "↖": 191, "↗": 192, "↘": 193, "↜": 194, "↝": 195, "↞": 196, "↟": 197, "↠": 198, "↡": 199, "↢": 200, "↣": 201, "↤": 202, "↥": 203, "↦": 204, "↧": 205, "↨": 206, "↪": 207, "↫": 208, "↬": 209, "↱": 210, "↲": 211, "↳": 212, "↴": 213, "↷": 214, "↸": 215, "↹": 216, "↺": 217, "↻": 218, "↾": 219, "⇀": 220, "⇁": 221, "⇂": 222, "⇅": 223, "⇇": 224, "⇉": 225, "⇊": 226, "⇋": 227, "⇌": 228, "⇐": 229, "⇑": 230, "⇒": 231, "⇓": 232, "⇔": 233, "⇕": 234, "⇖": 235, "⇗": 236, "⇘": 237, "⇚": 238, "⇜": 239, "⇝": 240, "⇞": 241, "⇟": 242, "⇠": 243, "⇡": 244, "⇢": 245, "⇣": 246, "⇤": 247, "⇥": 248, "⇦": 249, "⇧": 250, "⇨": 251, "⇪": 252, "⇫": 253, "⇭": 254, "⇮": 255, "⇯": 256, "⇱": 257, "⇲": 258, "⇳": 259, "⇴": 260, "⇵": 261, "⇷": 262, "⇸": 263, "⇹": 264, "⇺": 265, "⇻": 266, "⇿": 267, "∀": 268, "∁": 269, "∂": 270, "∃": 271, "∅": 272, "∆": 273, "∇": 274, "∊": 275, "∋": 276, "∍": 277, "∎": 278, "∐": 279, "∑": 280, "−": 281, "∔": 282, "∖": 283, "∗": 284, "∙": 285, "∜": 286, "∞": 287, "∠": 288, "∢": 289, "∣": 290, "∥": 291, "∧": 292, "∨": 293, "∩": 294, "∪": 295, "∭": 296, "∮": 297, "∯": 298, "∰": 299, "∱": 300, "∲": 301, "∴": 302, "∶": 303, "∷": 304, "∹": 305, "∺": 306, "∼": 307, "∿": 308, "≀": 309, "≂": 310, "≃": 311, "≅": 312, "≆": 313, "≈": 314, "≊": 315, "≋": 316, "≌": 317, "≍": 318, "≎": 319, "≑": 320, "≒": 321, "≔": 322, "≕": 323, "≘": 324, "≙": 325, "≚": 326, "≜": 327, "≝": 328, "≞": 329, "≟": 330, "≡": 331, "≤": 332, "≥": 333, "≦": 334, "≧": 335, "≨": 336, "≪": 337, "≫": 338, "≬": 339, "≲": 340, "≳": 341, "≶": 342, "≷": 343, "≺": 344, "≻": 345, "≼": 346, "≽": 347, "≾": 348, "≿": 349, "⊂": 350, "⊃": 351, "⊆": 352, "⊇": 353, "⊊": 354, "⊌": 355, "⊍": 356, "⊎": 357, "⊐": 358, "⊑": 359, "⊓": 360, "⊔": 361, "⊕": 362, "⊖": 363, "⊗": 364, "⊘": 365, "⊙": 366, "⊚": 367, "⊛": 368, "⊜": 369, "⊝": 370, "⊞": 371, "⊟": 372, "⊠": 373, "⊡": 374, "⊢": 375, "⊣": 376, "⊤": 377, "⊥": 378, "⊦": 379, "⊧": 380, "⊨": 381, "⊩": 382, "⊫": 383, "⊰": 384, "⊲": 385, "⊳": 386, "⊴": 387, "⊵": 388, "⊶": 389, "⊸": 390, "⊹": 391, "⊺": 392, "⊻": 393, "⊼": 394, "⊽": 395, "⊾": 396, "⊿": 397, "⋀": 398, "⋂": 399, "⋃": 400, "⋄": 401, "⋅": 402, "⋇": 403, "⋈": 404, "⋉": 405, "⋊": 406, "⋋": 407, "⋌": 408, "⋍": 409, "⋎": 410, "⋏": 411, "⋐": 412, "⋒": 413, "⋔": 414, "⋕": 415, "⋖": 416, "⋗": 417, "⋘": 418, "⋙": 419, "⋚": 420, "⋛": 421, "⋜": 422, "⋝": 423, "⋞": 424, "⋟": 425, "⋤": 426, "⋥": 427, "⋦": 428, "⋩": 429, "⋮": 430, "⋯": 431, "⋰": 432, "⋱": 433, "⋲": 434, "⋳": 435, "⋴": 436, "⋵": 437, "⋶": 438, "⋷": 439, "⋸": 440, "⋹": 441, "⋺": 442, "⋻": 443, "⋽": 444, "⋾": 445, "⋿": 446, "⌀": 447, "⌁": 448, "⌂": 449, "⌃": 450, "⌄": 451, "⌅": 452, "⌆": 453, "⌇": 454, "⌈": 455, "⌊": 456, "⌋": 457, "⌌": 458, "⌍": 459, "⌎": 460, "⌑": 461, "⌒": 462, "⌓": 463, "⌕": 464, "⌖": 465, "⌘": 466, "⌚": 467, "⌛": 468, "⌜": 469, "⌞": 470, "⌟": 471, "⌠": 472, "⌡": 473, "⌣": 474, "⌤": 475, "⌥": 476, "⌧": 477, "⌨": 478, "⌫": 479, "⌭": 480, "⌮": 481, "⌯": 482, "⌱": 483, "⌳": 484, "⌴": 485, "⌵": 486, "⌶": 487, "⌷": 488, "⌸": 489, "⌹": 490, "⌺": 491, "⌻": 492, "⌼": 493, "⌽": 494, "⌾": 495, "⍂": 496, "⍃": 497, "⍄": 498, "⍅": 499, "⍆": 500, "⍈": 501, "⍉": 502, "⍊": 503, "⍋": 504, "⍌": 505, "⍍": 506, "⍎": 507, "⍏": 508, "⍐": 509, "⍑": 510, "⍒": 511, "⍔": 512, "⍖": 513, "⍗": 514, "⍘": 515, "⍙": 516, "⍚": 517, "⍛": 518, "⍟": 519, "⍠": 520, "⍡": 521, "⍢": 522, "⍣": 523, "⍤": 524, "⍥": 525, "⍦": 526, "⍧": 527, "⍨": 528, "⍩": 529, "⍪": 530, "⍫": 531, "⍬": 532, "⍭": 533, "⍮": 534, "⍯": 535, "⍰": 536, "⍲": 537, "⍴": 538, "⍶": 539, "⍷": 540, "⍹": 541, "⍻": 542, "⍽": 543, "⍾": 544, "⍿": 545, "⎂": 546, "⎃": 547, "⎄": 548, "⎅": 549, "⎆": 550, "⎇": 551, "⎉": 552, "⎊": 553, "⎋": 554, "⎌": 555, "⎍": 556, "⎎": 557, "⎏": 558, "⎐": 559, "⎑": 560, "⎓": 561, "⎔": 562, "⎕": 563, "⎖": 564, "⎗": 565, "⎘": 566, "⎛": 567, "⎜": 568, "⎟": 569, "⎠": 570, "⎡": 571, "⎣": 572, "⎦": 573, "⎨": 574, "⎪": 575, "⎫": 576, "⎭": 577, "⎮": 578, "⎰": 579, "⎱": 580, "⎳": 581, "⎴": 582, "⎵": 583, "⎶": 584, "⎹": 585, "⎺": 586, "⎻": 587, "⎼": 588, "⎽": 589, "⎾": 590, "⎿": 591, "⏂": 592, "⏅": 593, "⏆": 594, "⏇": 595, "⏈": 596, "⏉": 597, "⏊": 598, "⏍": 599, "⏎": 600, "⏏": 601, "⏒": 602, "⏔": 603, "⏕": 604, "⏖": 605, "⏜": 606, "⏠": 607, "⏡": 608, "⏢": 609, "⏤": 610, "⏥": 611, "⏦": 612, "⏭": 613, "⏰": 614, "⏱": 615, "⏲": 616, "⏳": 617, "⏶": 618, "⏽": 619, "⏾": 620, "⏿": 621, "②": 622, "③": 623, "⑧": 624, "⑬": 625, "⑭": 626, "⑮": 627, "⑵": 628, "⑶": 629, "⑸": 630, "⑽": 631, "⒃": 632, "⒌": 633, "⒐": 634, "⒘": 635, "⒙": 636, "⒜": 637, "⒤": 638, "⒦": 639, "■": 640, "▢": 641, "▤": 642, "▦": 643, "▧": 644, "▨": 645, "▩": 646, "▭": 647, "▮": 648, "▰": 649, "▱": 650, "▲": 651, "△": 652, "▴": 653, "▵": 654, "▶": 655, "▹": 656, "▻": 657, "▽": 658, "▿": 659, "◀": 660, "◁": 661, "◂": 662, "◃": 663, "◅": 664, "◆": 665, "◇": 666, "◈": 667, "○": 668, "◌": 669, "◍": 670, "◎": 671, "●": 672, "◐": 673, "◑": 674, "◒": 675, "◓": 676, "◔": 677, "◕": 678, "◗": 679, "◙": 680, "◚": 681, "◛": 682, "◜": 683, "◝": 684, "◞": 685, "◟": 686, "◠": 687, "◡": 688, "◢": 689, "◥": 690, "◦": 691, "◧": 692, "◨": 693, "◩": 694, "◪": 695, "◫": 696, "◬": 697, "◭": 698, "◮": 699, "◯": 700, "◰": 701, "◲": 702, "◳": 703, "◴": 704, "◵": 705, "◶": 706, "◸": 707, "◹": 708, "◺": 709, "◻": 710, "◽": 711, "◾": 712, "◿": 713, "✀": 714, "✁": 715, "✂": 716, "✃": 717, "✅": 718, "✈": 719, "✋": 720, "✌": 721, "✍": 722, "✎": 723, "✏": 724, "✐": 725, "✑": 726, "✒": 727, "✔": 728, "✗": 729, "✙": 730, "✚": 731, "✞": 732, "✠": 733, "✢": 734, "✣": 735, "✥": 736, "✨": 737, "✩": 738, "✪": 739, "✫": 740, "✬": 741, "✮": 742, "✯": 743, "✰": 744, "✲": 745, "✳": 746, "✵": 747, "✶": 748, "✷": 749, "✸": 750, "✹": 751, "✺": 752, "✻": 753, "✼": 754, "✽": 755, "❀": 756, "❁": 757, "❂": 758, "❄": 759, "❅": 760, "❆": 761, "❈": 762, "❉": 763, "❊": 764, "❋": 765, "❌": 766, "❍": 767, "❎": 768, "❏": 769, "❐": 770, "❑": 771, "❒": 772, "❔": 773, "❕": 774, "❖": 775, "❗": 776, "❘": 777, "❚": 778, "❜": 779, "❝": 780, "❠": 781, "❡": 782, "❢": 783, "❤": 784, "❥": 785, "❦": 786, "❨": 787, "❪": 788, "❫": 789, "❮": 790, "❰": 791, "❲": 792, "❳": 793, "❵": 794, "❶": 795, "❷": 796, "❹": 797, "❺": 798, "❻": 799, "❼": 800, "❽": 801, "❾": 802, "➀": 803, "➁": 804, "➂": 805, "➃": 806, "➄": 807, "➅": 808, "➆": 809, "➇": 810, "➈": 811, "➉": 812, "➊": 813, "➋": 814, "➌": 815, "➍": 816, "➎": 817, "➏": 818, "➐": 819, "➑": 820, "➒": 821, "➓": 822, "➔": 823, "➖": 824, "➗": 825, "➘": 826, "➚": 827, "➜": 828, "➝": 829, "➞": 830, "➟": 831, "➣": 832, "➦": 833, "➧": 834, "➩": 835, "➪": 836, "➫": 837, "➬": 838, "➭": 839, "➮": 840, "➯": 841, "➰": 842, "➱": 843, "➲": 844, "➳": 845, "➶": 846, "➸": 847, "➹": 848, "➻": 849, "➼": 850, "➽": 851, "➾": 852, "➿": 853, "⟖": 854, "⟢": 855, "⠃": 856, "⠐": 857, "⠨": 858, "⠳": 859, "⡃": 860, "⡕": 861, "⡨": 862, "⡩": 863, "⡮": 864, "⢅": 865, "⢈": 866, "⢉": 867, "⢟": 868, "⢮": 869, "⣔": 870, "⣲": 871, "⤀": 872, "⤁": 873, "⤂": 874, "⤄": 875, "⤅": 876, "⤆": 877, "⤇": 878, "⤈": 879, "⤉": 880, "⤋": 881, "⤌": 882, "⤎": 883, "⤑": 884, "⤒": 885, "⤔": 886, "⤕": 887, "⤙": 888, "⤚": 889, "⤠": 890, "⤡": 891, "⤢": 892, "⤣": 893, "⤤": 894, "⤦": 895, "⤧": 896, "⤨": 897, "⤪": 898, "⤫": 899, "⤬": 900, "⤮": 901, "⤯": 902, "⤰": 903, "⤱": 904, "⤲": 905, "⤸": 906, "⤹": 907, "⤺": 908, "⤻": 909, "⤼": 910, "⤾": 911, "⥀": 912, "⥁": 913, "⥃": 914, "⥄": 915, "⥅": 916, "⥆": 917, "⥇": 918, "⥈": 919, "⥉": 920, "⥋": 921, "⥌": 922, "⥎": 923, "⥐": 924, "⥓": 925, "⥖": 926, "⥗": 927, "⥘": 928, "⥙": 929, "⥚": 930, "⥛": 931, "⥟": 932, "⥣": 933, "⥤": 934, "⥥": 935, "⥦": 936, "⥧": 937, "⥨": 938, "⥪": 939, "⥫": 940, "⥬": 941, "⥭": 942, "⥰": 943, "⥱": 944, "⥲": 945, "⥵": 946, "⥶": 947, "⥷": 948, "⥸": 949, "⥹": 950, "⥺": 951, "⥽": 952, "⥾": 953, "⦀": 954, "⦁": 955, "⦂": 956, "⦃": 957, "⦄": 958, "⦅": 959, "⦆": 960, "⦇": 961, "⦈": 962, "⦉": 963, "⦊": 964, "⦋": 965, "⦏": 966, "⦐": 967, "⦑": 968, "⦓": 969, "⦔": 970, "⦖": 971, "⦜": 972, "⦝": 973, "⦞": 974, "⦟": 975, "⦡": 976, "⦣": 977, "⦥": 978, "⦧": 979, "⦨": 980, "⦪": 981, "⦭": 982, "⦮": 983, "⦯": 984, "⦲": 985, "⦳": 986, "⦴": 987, "⦶": 988, "⦷": 989, "⦹": 990, "⦻": 991, "⦼": 992, "⦽": 993, "⦾": 994, "⦿": 995, "⧀": 996, "⧂": 997, "⧇": 998, "⧊": 999, "⧋": 1000, "⧍": 1001, "⧎": 1002, "⧏": 1003, "⧑": 1004, "⧒": 1005, "⧕": 1006, "⧖": 1007, "⧘": 1008, "⧙": 1009, "⧜": 1010, "⧞": 1011, "⧢": 1012, "⧣": 1013, "⧥": 1014, "⧧": 1015, "⧨": 1016, "⧪": 1017, "⧭": 1018, "⧳": 1019, "⧴": 1020, "⧵": 1021, "⧷": 1022, "⧹": 1023, "⧼": 1024, "⧾": 1025, "⨀": 1026, "⨁": 1027, "⨂": 1028, "⨃": 1029, "⨅": 1030, "⨇": 1031, "⨉": 1032, "⨍": 1033, "⨎": 1034, "⨏": 1035, "⨐": 1036, "⨒": 1037, "⨔": 1038, "⨖": 1039, "⨙": 1040, "⨚": 1041, "⨛": 1042, "⨡": 1043, "⨣": 1044, "⨤": 1045, "⨥": 1046, "⨧": 1047, "⨩": 1048, "⨫": 1049, "⨬": 1050, "⨭": 1051, "⨮": 1052, "⨰": 1053, "⨱": 1054, "⨳": 1055, "⨴": 1056, "⨵": 1057, "⨷": 1058, "⨸": 1059, "⨺": 1060, "⨻": 1061, "⨼": 1062, "⨾": 1063, "⩁": 1064, "⩃": 1065, "⩄": 1066, "⩅": 1067, "⩈": 1068, "⩉": 1069, "⩊": 1070, "⩋": 1071, "⩌": 1072, "⩍": 1073, "⩎": 1074, "⩏": 1075, "⩐": 1076, "⩑": 1077, "⩒": 1078, "⩔": 1079, "⩕": 1080, "⩖": 1081, "⩗": 1082, "⩘": 1083, "⩙": 1084, "⩚": 1085, "⩛": 1086, "⩜": 1087, "⩝": 1088, "⩞": 1089, "⩟": 1090, "⩠": 1091, "⩤": 1092, "⩧": 1093, "⩨": 1094, "⩩": 1095, "⩪": 1096, "⩫": 1097, "⩬": 1098, "⩭": 1099, "⩮": 1100, "⩯": 1101, "⩰": 1102, "⩱": 1103, "⩲": 1104, "⩴": 1105, "⩵": 1106, "⩸": 1107, "⩹": 1108, "⩺": 1109, "⩻": 1110, "⩼": 1111, "⩽": 1112, "⩾": 1113, "⪁": 1114, "⪄": 1115, "⪅": 1116, "⪆": 1117, "⪇": 1118, "⪉": 1119, "⪊": 1120, "⪋": 1121, "⪌": 1122, "⪎": 1123, "⪏": 1124, "⪐": 1125, "⪒": 1126, "⪓": 1127, "⪔": 1128, "⪕": 1129, "⪗": 1130, "⪙": 1131, "⪝": 1132, "⪞": 1133, "⪡": 1134, "⪥": 1135, "⪨": 1136, "⪪": 1137, "⪭": 1138, "⪮": 1139, "⪰": 1140, "⪱": 1141, "⪶": 1142, "⪷": 1143, "⪹": 1144, "⪼": 1145, "⪽": 1146, "⪾": 1147, "⪿": 1148, "⫀": 1149, "⫁": 1150, "⫂": 1151, "⫄": 1152, "⫆": 1153, "⫉": 1154, "⫋": 1155, "⫍": 1156, "⫎": 1157, "⫏": 1158, "⫐": 1159, "⫑": 1160, "⫒": 1161, "⫓": 1162, "⫖": 1163, "⫗": 1164, "⫝": 1165, "⫟": 1166, "⫣": 1167, "⫤": 1168, "⫥": 1169, "⫧": 1170, "⫨": 1171, "⫪": 1172, "⫫": 1173, "⫮": 1174, "⫱": 1175, "⫲": 1176, "⫵": 1177, "⫶": 1178, "⫷": 1179, "⫸": 1180, "⫹": 1181, "⫽": 1182, "⬀": 1183, "⬂": 1184, "⬃": 1185, "⬅": 1186, "⬇": 1187, "⬉": 1188, "⬌": 1189, "⬍": 1190, "⬎": 1191, "⬏": 1192, "⬑": 1193, "⬒": 1194, "⬔": 1195, "⬕": 1196, "⬖": 1197, "⬗": 1198, "⬘": 1199, "⬚": 1200, "⬛": 1201, "⬡": 1202, "⬢": 1203, "⬥": 1204, "⬧": 1205, "⬨": 1206, "⬬": 1207, "⬭": 1208, "⬮": 1209, "⬯": 1210, "⬱": 1211, "⬲": 1212, "⬳": 1213, "⬵": 1214, "⬶": 1215, "⬸": 1216, "⬺": 1217, "⬻": 1218, "⬼": 1219, "⬽": 1220, "⭁": 1221, "⭃": 1222, "⭄": 1223, "⭅": 1224, "⭆": 1225, "⭇": 1226, "⭈": 1227, "⭊": 1228, "⭌": 1229, "⭍": 1230, "⭎": 1231, "⭏": 1232, "⭑": 1233, "⭕": 1234, "⭖": 1235, "⭙": 1236, "⭚": 1237, "⭛": 1238, "⭞": 1239, "⭠": 1240, "⭡": 1241, "⭢": 1242, "⭣": 1243, "⭤": 1244, "⭥": 1245, "⭧": 1246, "⭨": 1247, "⭩": 1248, "⭪": 1249, "⭬": 1250, "⭮": 1251, "⭯": 1252, "⭰": 1253, "⭳": 1254, "⭴": 1255, "⭶": 1256, "⭷": 1257, "⭸": 1258, "⭹": 1259, "⭺": 1260, "⭻": 1261, "⭼": 1262, "⭽": 1263, "⭾": 1264, "⮂": 1265, "⮄": 1266, "⮅": 1267, "⮆": 1268, "⮇": 1269, "⮉": 1270, "⮊": 1271, "⮋": 1272, "⮌": 1273, "⮍": 1274, "⮏": 1275, "⮐": 1276, "⮑": 1277, "⮒": 1278, "⮓": 1279, "⮕": 1280, "⮖": 1281, "⮗": 1282, "⮘": 1283, "⮙": 1284, "⮜": 1285, "⮝": 1286, "⮟": 1287, "⮠": 1288, "⮡": 1289, "⮣": 1290, "⮤": 1291, "⮥": 1292, "⮦": 1293, "⮩": 1294, "⮪": 1295, "⮫": 1296, "⮮": 1297, "⮱": 1298, "⮲": 1299, "⮳": 1300, "⮴": 1301, "⮵": 1302, "⮶": 1303, "⮷": 1304, "⮺": 1305, "⮻": 1306, "⮾": 1307, "⮿": 1308, "⯁": 1309, "⯅": 1310, "⯆": 1311, "⯇": 1312, "⯈": 1313, "⯊": 1314, "⯋": 1315, "⯌": 1316, "⯍": 1317, "⯏": 1318, "⯑": 1319, "⯒": 1320, "⯓": 1321, "⯔": 1322, "⯕": 1323, "⯗": 1324, "⯚": 1325, "⯛": 1326, "⯜": 1327, "⯟": 1328, "⯢": 1329, "⯥": 1330, "⯦": 1331, "⯧": 1332, "⯨": 1333, "⯩": 1334, "⯪": 1335, "⯫": 1336, "⯭": 1337, "⯮": 1338, "⯰": 1339, "⯱": 1340, "⯳": 1341, "⯴": 1342, "⯵": 1343, "⯶": 1344, "⯷": 1345, "⯸": 1346, "⯹": 1347, "⯺": 1348, "⯻": 1349, "⯽": 1350, "Ɫ": 1351, "ⱴ": 1352, "ⱻ": 1353, "⸊": 1354, "⸩": 1355, "⸪": 1356, "⸬": 1357, "⹁": 1358, "〈": 1359, "〉": 1360, "ꜥ": 1361, "Ꝗ": 1362, "ꝙ": 1363, "ꝿ": 1364, "ꞏ": 1365, "Ɡ": 1366, "ꟃ": 1367, "Ꞔ": 1368, "ꟳ": 1369, "ꬵ": 1370, "ꬾ": 1371, "ꭐ": 1372, "ꭖ": 1373, "ꭞ": 1374, "𝐋": 1375, "𝐡": 1376, "𝑍": 1377, "𝑎": 1378, "𝑜": 1379, "𝑨": 1380, "𝑶": 1381, "𝑷": 1382, "𝒀": 1383, "𝒊": 1384, "𝒍": 1385, "𝒒": 1386, "𝒞": 1387, "𝒪": 1388, "𝒴": 1389, "𝒹": 1390, "𝓏": 1391, "𝓝": 1392, "𝓹": 1393, "𝔅": 1394, "𝔪": 1395, "𝔻": 1396, "𝕃": 1397, "𝕚": 1398, "𝕫": 1399, "𝕱": 1400, "𝖦": 1401, "𝖸": 1402, "𝗀": 1403, "𝗇": 1404, "𝗗": 1405, "𝘉": 1406, "𝘓": 1407, "𝘥": 1408, "𝘶": 1409, "𝙯": 1410, "𝙺": 1411, "𝚎": 1412, "𝚢": 1413, "𝚳": 1414, "𝚶": 1415, "𝛂": 1416, "𝛺": 1417, "𝛽": 1418, "𝜁": 1419, "𝜬": 1420, "𝝘": 1421, "𝝟": 1422, "𝞂": 1423, "𝞚": 1424, "𝞝": 1425, "𝞤": 1426, "𝞼": 1427, "𝞽": 1428, "𝟈": 1429, "𝟏": 1430, "𝟜": 1431, "𝟫": 1432, "𝟬": 1433, "𝟷": 1434, "🚕": 1435, "🚖": 1436, "🚗": 1437, "🚘": 1438, "🚙": 1439, "🚚": 1440, "🚛": 1441, "🚜": 1442, "🚝": 1443, "🚞": 1444, "🚩": 1445, "🚪": 1446, "🚫": 1447, "🚬": 1448, "🚸": 1449, "🚹": 1450, "🚻": 1451, "🚿": 1452, "🛁": 1453, "🛄": 1454, "🛅": 1455, "🛆": 1456, "🛇": 1457, "🛈": 1458, "🛉": 1459, "🜁": 1460, "🜂": 1461, "🜃": 1462, "🜈": 1463, "🜉": 1464, "🜊": 1465, "🜍": 1466, "🜎": 1467, "🜏": 1468, "🜔": 1469, "🜗": 1470, "🜘": 1471, "🜛": 1472, "🜝": 1473, "🜢": 1474, "🜭": 1475, "🜱": 1476, "🜲": 1477, "🜳": 1478, "🜵": 1479, "🜷": 1480, "🜼": 1481, "🜿": 1482, "🝁": 1483, "🝃": 1484, "🝅": 1485, "🝇": 1486, "🝉": 1487, "🝊": 1488, "🝋": 1489, "🝏": 1490, "🝐": 1491, "🝓": 1492, "🝔": 1493, "🝕": 1494, "🝗": 1495, "🝘": 1496, "🝙": 1497, "🝚": 1498, "🝜": 1499, "🝝": 1500, "🝠": 1501, "🝩": 1502, "🝪": 1503, "🝭": 1504, "🝲": 1505, "🝵": 1506, "🝸": 1507, "🞀": 1508, "🞂": 1509, "🞆": 1510, "🞇": 1511, "🞊": 1512, "🞌": 1513, "🞎": 1514, "🞏": 1515, "🞒": 1516, "🞕": 1517, "🞘": 1518, "🞚": 1519, "🞛": 1520, "🞞": 1521, "🞟": 1522, "🞠": 1523, "🞡": 1524, "🞤": 1525, "🞧": 1526, "🞨": 1527, "🞫": 1528, "🞬": 1529, "🞮": 1530, "🞯": 1531, "🞰": 1532, "🞱": 1533, "🞲": 1534, "🞳": 1535, "🞴": 1536, "🞵": 1537, "🞶": 1538, "🞷": 1539, "🞹": 1540, "🞾": 1541, "🞿": 1542, "🟀": 1543, "🟁": 1544, "🟂": 1545, "🟃": 1546, "🟄": 1547, "🟆": 1548, "🟇": 1549, "🟈": 1550, "🟉": 1551, "🟊": 1552, "🟌": 1553, "🟍": 1554, "🟎": 1555, "🟏": 1556, "🟐": 1557, "🟒": 1558, "🟓": 1559, "🟔": 1560, "🟕": 1561, "🟗": 1562, "🟘": 1563, "🟙": 1564, "🟚": 1565, "🟝": 1566, "🟠": 1567, "🟡": 1568, "🟢": 1569, "🟣": 1570, "🟫": 1571, "🟮": 1572, "🟱": 1573, "🟳": 1574, "🟶": 1575, "🟷": 1576, "🟹": 1577, "🟻": 1578, "🠀": 1579, "🠁": 1580, "🠂": 1581, "🠃": 1582, "🠆": 1583, "🠈": 1584, "🠘": 1585, "🠚": 1586, "🠛": 1587, "🠝": 1588, "🠠": 1589, "🠡": 1590, "🠢": 1591, "🠤": 1592, "🠥": 1593, "🠦": 1594, "🠧": 1595, "🠪": 1596, "🠫": 1597, "🠭": 1598, "🠲": 1599, "🠳": 1600, "🠴": 1601, "🠷": 1602, "🠽": 1603, "🠾": 1604, "🠿": 1605, "🡅": 1606, "🡇": 1607, "🡈": 1608, "🡌": 1609, "🡎": 1610, "🡔": 1611, "🡘": 1612, "🡛": 1613, "🡞": 1614, "🡠": 1615, "🡨": 1616, "🡩": 1617, "🡴": 1618, "🡵": 1619, "🡼": 1620, "🢀": 1621, "🢁": 1622, "🢂": 1623, "🢃": 1624, "🢋": 1625, "🢎": 1626, "🢓": 1627, "🢔": 1628, "🢕": 1629, "🢘": 1630, "🢙": 1631, "🢚": 1632, "🢛": 1633, "🢣": 1634, "🢥": 1635, "🢧": 1636, "🢯": 1637, "🢱": 1638, "🢳": 1639, "🢺": 1640, "🣂": 1641, "🣃": 1642, "🣆": 1643, "##1": 1644, "##n": 1645, "##g": 1646, "##a": 1647, "##t": 1648, "##e": 1649, "##o": 1650, "##r": 1651, "##y": 1652, "##c": 1653, "##l": 1654, "##i": 1655, "##2": 1656, "##d": 1657, "##4": 1658, "##E": 1659, "##s": 1660, "##f": 1661, "##u": 1662, "##p": 1663, "##m": 1664, "##z": 1665, "##8": 1666, "##h": 1667, "##6": 1668, "##3": 1669, "##P": 1670, "##⋌": 1671, "##9": 1672, "##x": 1673, "##5": 1674, "##0": 1675, "##⮩": 1676, "##⬬": 1677, "##⫨": 1678, "##🞳": 1679, "##𝚶": 1680, "##T": 1681, "##A": 1682, "##R": 1683, "##𝟬": 1684, "##7": 1685, "##⎼": 1686, "##k": 1687, "##𝑜": 1688, "##🛇": 1689, "##⇊": 1690, "##⅐": 1691, "##b": 1692, "##⭻": 1693, "##⍊": 1694, "##j": 1695, "##w": 1696, "##Ⅴ": 1697, "##ⅴ": 1698, "##G": 1699, "##v": 1700, "##⧎": 1701, "##⊃": 1702, "##Ⅳ": 1703, "##N": 1704, "##D": 1705, "##⧏": 1706, "##⧜": 1707, "##𝚎": 1708, "ng": 1709, "##on": 1710, "##ti": 1711, "##tion": 1712, "##or": 1713, "##og": 1714, "##nc": 1715, "##er": 1716, "##ation": 1717, "##te": 1718, "##ic": 1719, "##ri": 1720, "##nt": 1721, "re": 1722, "log": 1723, "##at": 1724, "logic": 1725, "##al": 1726, "##ec": 1727, "##ition": 1728, "##ng": 1729, "##su": 1730, "##nce": 1731, "##st": 1732, "me": 1733, "co": 1734, "##ty": 1735, "##ta": 1736, "##um": 1737, "qu": 1738, "##ing": 1739, "##ist": 1740, "##ma": 1741, "meta": 1742, "##ant": 1743, "quant": 1744, "quantum": 1745, "##nition": 1746, "##ognition": 1747, "op": 1748, "##for": 1749, "mat": 1750, "##sul": 1751, "##rity": 1752, "##de": 1753, "##ute": 1754, "##he": 1755, "cognition": 1756, "##sult": 1757, "dist": 1758, "##bute": 1759, "##ribute": 1760, "distribute": 1761, "distributed": 1762, "ai": 1763, "sec": 1764, "##urity": 1765, "security": 1766, "math": 1767, "per": 1768, "##mance": 1769, "##formance": 1770, "performance": 1771, "code": 1772, "result": 1773, "##ss": 1774, "##mp": 1775, "oper": 1776, "fu": 1777, "con": 1778, "##ur": 1779, "operation": 1780, "##iz": 1781, "in": 1782, "##ro": 1783, "##le": 1784, "##ent": 1785, "##nction": 1786, "##ization": 1787, "##it": 1788, "##re": 1789, "##ce": 1790, "##la": 1791, "##se": 1792, "##ef": 1793, "au": 1794, "##ra": 1795, "##tur": 1796, "retur": 1797, "return": 1798, "pro": 1799, "The": 1800, "function": 1801, "def": 1802, "##hen": 1803, "##teg": 1804, "##di": 1805, "##lic": 1806, "##ali": 1807, "##cess": 1808, "for": 1809, "##ment": 1810, "##ection": 1811, "##her": 1812, "NG": 1813, "##mb": 1814, "##ter": 1815, "as": 1816, "ne": 1817, "te": 1818, "aut": 1819, "##sure": 1820, "process": 1821, "##tem": 1822, "##lication": 1823, "##tim": 1824, "optim": 1825, "el": 1826, "else": 1827, "##ly": 1828, "##ar": 1829, "imp": 1830, "to": 1831, "ca": 1832, "##tor": 1833, "##ith": 1834, "##as": 1835, "##ory": 1836, "##ne": 1837, "par": 1838, "##lass": 1839, "##ence": 1840, "##king": 1841, "##atter": 1842, "##attern": 1843, "cons": 1844, "##hi": 1845, "integ": 1846, "comp": 1847, "##li": 1848, "##rr": 1849, "##rror": 1850, "##un": 1851, "ref": 1852, "item": 1853, "##zy": 1854, "##zzy": 1855, "fuzzy": 1856, "at": 1857, "##ntion": 1858, "##tention": 1859, "attention": 1860, "##ync": 1861, "async": 1862, "##nd": 1863, "##lement": 1864, "if": 1865, "##os": 1866, "##osition": 1867, "al": 1868, "mo": 1869, "##gor": 1870, "##dal": 1871, "##ithm": 1872, "algor": 1873, "modal": 1874, "algorithm": 1875, "##fi": 1876, "be": 1877, "##ry": 1878, "##ns": 1879, "enc": 1880, "##ate": 1881, "##oral": 1882, "##mporal": 1883, "temporal": 1884, "bic": 1885, "pattern": 1886, "##dition": 1887, "##ondition": 1888, "optimization": 1889, "bicondition": 1890, "biconditional": 1891, "##tic": 1892, "##pu": 1893, "##cur": 1894, "implication": 1895, "##curs": 1896, "list": 1897, "##gation": 1898, "negation": 1899, "##unction": 1900, "##junction": 1901, "conjunction": 1902, "##el": 1903, "the": 1904, "EN": 1905, "ST": 1906, "##AR": 1907, "END": 1908, "STAR": 1909, "START": 1910, "dat": 1911, "data": 1912, "38": 1913, "##ru": 1914, "##ol": 1915, "##ctor": 1916, "##lo": 1917, "##ai": 1918, "25": 1919, "func": 1920, "74": 1921, "49": 1922, "la": 1923, "##da": 1924, "##mbda": 1925, "lambda": 1926, "42": 1927, "66": 1928, "##ch": 1929, "##oning": 1930, "reas": 1931, "reasoning": 1932, "70": 1933, "32": 1934, "71": 1935, "class": 1936, "##ed": 1937, "56": 1938, "57": 1939, "75": 1940, "ex": 1941, "77": 1942, "60": 1943, "79": 1944, "If": 1945, "then": 1946, "30": 1947, "45": 1948, "whi": 1949, "while": 1950, "73": 1951, "85": 1952, "ha": 1953, "##ndle": 1954, "handle": 1955, "48": 1956, "52": 1957, "ag": 1958, "th": 1959, "agent": 1960, "that": 1961, "53": 1962, "59": 1963, "68": 1964, "##ine": 1965, "80": 1966, "neur": 1967, "40": 1968, "26": 1969, "43": 1970, "86": 1971, "33": 1972, "76": 1973, "83": 1974, "67": 1975, "81": 1976, "##cognition": 1977, "metacognition": 1978, "31": 1979, "36": 1980, "61": 1981, "63": 1982, "gra": 1983, "pla": 1984, "##nn": 1985, "##dient": 1986, "gradient": 1987, "plann": 1988, "planner": 1989, "39": 1990, "55": 1991, "error": 1992, "##rking": 1993, "##hma": 1994, "##nchma": 1995, "benchma": 1996, "benchmarking": 1997, "41": 1998, "78": 1999, "##tegory": 2000, "category": 2001, "34": 2002, "46": 2003, "50": 2004, "##mory": 2005, "memory": 2006, "29": 2007, "27": 2008, "35": 2009, "69": 2010, "84": 2011, "dec": 2012, "us": 2013, "##oher": 2014, "##lection": 2015, "##ita": 2016, "##tition": 2017, "inher": 2018, "partition": 2019, "reflection": 2020, "decoher": 2021, "using": 2022, "##itance": 2023, "inheritance": 2024, "decoherence": 2025, "##nation": 2026, "##is": 2027, "##ordi": 2028, "##ncy": 2029, "##tency": 2030, "coordi": 2031, "consis": 2032, "coordination": 2033, "consistency": 2034, "24": 2035, "47": 2036, "62": 2037, "sum": 2038, "##lization": 2039, "##alle": 2040, "paralle": 2041, "parallelization": 2042, "82": 2043, "72": 2044, "##nti": 2045, "##denti": 2046, "confi": 2047, "##ality": 2048, "##dentiality": 2049, "confidentiality": 2050, "##ral": 2051, "integral": 2052, "28": 2053, "58": 2054, "sali": 2055, "salience": 2056, "51": 2057, "When": 2058, "lo": 2059, "##op": 2060, "composition": 2061, "loop": 2062, "65": 2063, "av": 2064, "##asure": 2065, "##ili": 2066, "##plication": 2067, "##bili": 2068, "replication": 2069, "measure": 2070, "##labili": 2071, "##ailabili": 2072, "availabili": 2073, "measurement": 2074, "availability": 2075, "ch": 2076, "##unking": 2077, "chunking": 2078, "44": 2079, "##dit": 2080, "audit": 2081, "##hentic": 2082, "authentic": 2083, "authentication": 2084, "bi": 2085, "gate": 2086, "##ance": 2087, "##ou": 2088, "##ci": 2089, "##sne": 2090, "##hor": 2091, "author": 2092, "consci": 2093, "compli": 2094, "bias": 2095, "##ousne": 2096, "authorization": 2097, "consciousne": 2098, "compliance": 2099, "consciousness": 2100, "37": 2101, "64": 2102, "integrity": 2103, "cor": 2104, "tun": 2105, "##rection": 2106, "correction": 2107, "tuning": 2108, "quali": 2109, "tens": 2110, "qualia": 2111, "tensor": 2112, "clo": 2113, "##pol": 2114, "##ogy": 2115, "topol": 2116, "closure": 2117, "topology": 2118, "sc": 2119, "##ion": 2120, "recurs": 2121, "##aling": 2122, "scaling": 2123, "recursion": 2124, "ast": 2125, "vec": 2126, "##ize": 2127, "optimize": 2128, "##torization": 2129, "vectorization": 2130, "go": 2131, "##nsu": 2132, "##ip": 2133, "##ption": 2134, "##ssip": 2135, "conse": 2136, "##ryption": 2137, "encryption": 2138, "gossip": 2139, "##nsus": 2140, "consensus": 2141, "##actor": 2142, "##rog": 2143, "##prog": 2144, "##mm": 2145, "metaprog": 2146, "##ramm": 2147, "refactor": 2148, "metaprogramm": 2149, "metaprogramming": 2150, "non": 2151, "##repu": 2152, "##diation": 2153, "nonrepu": 2154, "nonrepudiation": 2155, "54": 2156, "ra": 2157, "try": 2158, "##ft": 2159, "##pt": 2160, "##cept": 2161, "except": 2162, "raft": 2163, "int": 2164, "su": 2165, "##sp": 2166, "##per": 2167, "##position": 2168, "##rosp": 2169, "introsp": 2170, "super": 2171, "introspection": 2172, "superposition": 2173, "17": 2174, "emb": 2175, "##ding": 2176, "##edding": 2177, "embedding": 2178, "Class": 2179, "neural": 2180, "cach": 2181, "caching": 2182, "ent": 2183, "##ap": 2184, "##ang": 2185, "##sulation": 2186, "encap": 2187, "entang": 2188, "encapsulation": 2189, "entanglement": 2190, "15": 2191, "##ling": 2192, "profi": 2193, "profiling": 2194, "##rix": 2195, "matrix": 2196, "po": 2197, "tra": 2198, "##sm": 2199, "##phi": 2200, "##mor": 2201, "##mer": 2202, "##former": 2203, "const": 2204, "##lymor": 2205, "##nsformer": 2206, "polymor": 2207, "transformer": 2208, "##phism": 2209, "polymorphism": 2210, "ab": 2211, "sel": 2212, "##ction": 2213, "##stra": 2214, "abstra": 2215, "self": 2216, "abstraction": 2217, "Def": 2218, "Define": 2219, "Cre": 2220, "inpu": 2221, "Create": 2222, "input": 2223, "Ap": 2224, "##tter": 2225, "##ply": 2226, "better": 2227, "Apply": 2228, "Imp": 2229, "and": 2230, "Implement": 2231, "14": 2232, "16": 2233, "19": 2234, "87": 2235, "##iel": 2236, "##ield": 2237, "13": 2238, "en": 2239, "##sures": 2240, "ensures": 2241, "20": 2242, "oc": 2243, "sy": 2244, "##oma": 2245, "##stem": 2246, "##ally": 2247, "automa": 2248, "##tically": 2249, "occurs": 2250, "system": 2251, "automatically": 2252, "##rom": 2253, "use": 2254, "##tru": 2255, "##ct": 2256, "uses": 2257, "##truct": 2258, "18": 2259, "returns": 2260, "12": 2261, "##ort": 2262, "import": 2263, "21": 2264, "10": 2265, "Error": 2266, "Re": 2267, "aw": 2268, "fn": 2269, "##ait": 2270, "Result": 2271, "await": 2272, "init": 2273, "le": 2274, "of": 2275, "let": 2276, "Si": 2277, "ther": 2278, "##efor": 2279, "Since": 2280, "therefor": 2281, "therefore": 2282, "11": 2283, "var": 2284, "##ructor": 2285, "constructor": 2286, "Sy": 2287, "##olic": 2288, "##mbolic": 2289, "Symbolic": 2290, "Pattern": 2291, "val": 2292, "yield": 2293, "##au": 2294, "##lt": 2295, "##ue": 2296, "match": 2297, "defau": 2298, "value": 2299, "default": 2300, "No": 2301, "Not": 2302, "##Error": 2303, "From": 2304, "fol": 2305, "it": 2306, "##ws": 2307, "##lows": 2308, "follows": 2309, "Eit": 2310, "with": 2311, "##ator": 2312, "comb": 2313, "operator": 2314, "##rocess": 2315, "##ines": 2316, "Either": 2317, "combines": 2318, "##rocessor": 2319, "Gi": 2320, "ded": 2321, "we": 2322, "##en": 2323, "##uce": 2324, "##ven": 2325, "can": 2326, "Given": 2327, "deduce": 2328, "Tru": 2329, "##ecute": 2330, "execute": 2331, "True": 2332, "Struct": 2333, "field": 2334, "i3": 2335, "struct": 2336, "implement": 2337, "i32": 2338, "implements": 2339, "22": 2340, "from": 2341, "mai": 2342, "##Processor": 2343, "main": 2344, "Be": 2345, "##gi": 2346, "##ph": 2347, "##ogly": 2348, "neurogly": 2349, "Begi": 2350, "neuroglyph": 2351, "Begin": 2352, "Processor": 2353, "78ng": 2354, "56ng": 2355, "68ng": 2356, "32ng": 2357, "75ng": 2358, "82ng": 2359, "48ng": 2360, "86ng": 2361, "83ng": 2362, "16ng": 2363, "25ng": 2364, "70ng": 2365, "57ng": 2366, "30ng": 2367, "80ng": 2368, "31ng": 2369, "36ng": 2370, "55ng": 2371, "41ng": 2372, "51ng": 2373, "20ng": 2374, "21ng": 2375, "74ng": 2376, "52ng": 2377, "59ng": 2378, "43ng": 2379, "76ng": 2380, "34ng": 2381, "29ng": 2382, "35ng": 2383, "58ng": 2384, "17ng": 2385, "87ng": 2386, "49Error": 2387, "42ng": 2388, "77ng": 2389, "79ng": 2390, "45ng": 2391, "45Processor": 2392, "73ng": 2393, "85ng": 2394, "26ng": 2395, "33ng": 2396, "81ng": 2397, "63ng": 2398, "41Error": 2399, "24ng": 2400, "62ng": 2401, "72ng": 2402, "65ng": 2403, "44ng": 2404, "64ng": 2405, "15ng": 2406, "23": 2407, "6Error": 2408, "7ng": 2409, "8ng": 2410, "38ng": 2411, "49ng": 2412, "42Processor": 2413, "71ng": 2414, "75Error": 2415, "30Error": 2416, "53ng": 2417, "40ng": 2418, "83Processor": 2419, "61ng": 2420, "39ng": 2421, "50ng": 2422, "29Error": 2423, "27ng": 2424, "69ng": 2425, "47ng": 2426, "47Error": 2427, "62Error": 2428, "62Processor": 2429, "82Error": 2430, "28ng": 2431, "28Error": 2432, "44Error": 2433, "37ng": 2434, "17Error": 2435, "14Processor": 2436, "19Error": 2437, "13ng": 2438, "11ng": 2439, "1ng": 2440, "1Error": 2441, "3ng": 2442, "4ng": 2443, "5ng": 2444, "6ng": 2445, "8Processor": 2446, "9ng": 2447, "ℛng": 2448, "↨⎼": 2449, "≳⍊": 2450, "⊖⋌": 2451, "⋎⧏": 2452, "⋖⬬": 2453, "⌍⊃": 2454, "⎘⧎": 2455, "⏅⧜": 2456, "⏜⮩": 2457, "⏡⫨": 2458, "⦾🞳": 2459, "⯌🛇": 2460, "ꞏng": 2461, "𝐋Processor": 2462, "🢃⭻": 2463, "##𝟬ng": 2464, "##⇊⅐": 2465, "##Ⅴng": 2466, "##Ⅳng": 2467, "##𝚎ng": 2468, "38Processor": 2469, "49Processor": 2470, "42Ⅳng": 2471, "66ng": 2472, "66Processor": 2473, "70Error": 2474, "70Processor": 2475, "56Error": 2476, "57Error": 2477, "57Processor": 2478, "60ng": 2479, "60Error": 2480, "60Processor": 2481, "79Processor": 2482, "30Processor": 2483, "73Processor": 2484, "48ⅴ": 2485, "48𝟬ng": 2486, "52Error": 2487, "53Error": 2488, "59Error": 2489, "68Error": 2490, "68Processor": 2491, "80Processor": 2492, "26Processor": 2493, "43Error": 2494, "83Error": 2495, "31Processor": 2496, "36Processor": 2497, "61Processor": 2498, "63Error": 2499, "63Processor": 2500, "39Processor": 2501, "55Error": 2502, "78Error": 2503, "78Processor": 2504, "46ng": 2505, "46Error": 2506, "46Processor": 2507, "27Error": 2508, "27Processor": 2509, "35Processor": 2510, "69Error": 2511, "69Processor": 2512, "82𝚶": 2513, "58Error": 2514, "51𝑜": 2515, "51Processor": 2516, "44Processor": 2517, "64Error": 2518, "54ng": 2519, "54Processor": 2520, "15Ⅴng": 2521, "14ng": 2522, "19ng": 2523, "13Error": 2524, "20Processor": 2525, "18ng": 2526, "18Error": 2527, "21Error": 2528, "10ng": 2529, "10𝚎ng": 2530, "11Error": 2531, "⯌🛇⇊⅐": 2532}, "merges": [["n", "##g"], ["##o", "##n"], ["##t", "##i"], ["##ti", "##on"], ["##o", "##r"], ["##o", "##g"], ["##n", "##c"], ["##e", "##r"], ["##a", "##tion"], ["##t", "##e"], ["##i", "##c"], ["##r", "##i"], ["##n", "##t"], ["r", "##e"], ["l", "##og"], ["##a", "##t"], ["log", "##ic"], ["##a", "##l"], ["##e", "##c"], ["##i", "##tion"], ["##n", "##g"], ["##s", "##u"], ["##nc", "##e"], ["##s", "##t"], ["m", "##e"], ["c", "##o"], ["##t", "##y"], ["##t", "##a"], ["##u", "##m"], ["q", "##u"], ["##i", "##ng"], ["##i", "##st"], ["##m", "##a"], ["me", "##ta"], ["##a", "##nt"], ["qu", "##ant"], ["quant", "##um"], ["##n", "##ition"], ["##og", "##nition"], ["o", "##p"], ["##f", "##or"], ["m", "##at"], ["##su", "##l"], ["##ri", "##ty"], ["##d", "##e"], ["##u", "##te"], ["##h", "##e"], ["c", "##ognition"], ["##sul", "##t"], ["d", "##ist"], ["##b", "##ute"], ["##ri", "##bute"], ["dist", "##rib<PERSON>"], ["distribute", "##d"], ["a", "##i"], ["s", "##ec"], ["##u", "##rity"], ["sec", "##urity"], ["mat", "##h"], ["p", "##er"], ["##ma", "##nce"], ["##for", "##mance"], ["per", "##formance"], ["co", "##de"], ["re", "##sult"], ["##s", "##s"], ["##m", "##p"], ["op", "##er"], ["f", "##u"], ["c", "##on"], ["##u", "##r"], ["oper", "##ation"], ["##i", "##z"], ["i", "##n"], ["##r", "##o"], ["##l", "##e"], ["##e", "##nt"], ["##nc", "##tion"], ["##iz", "##ation"], ["##i", "##t"], ["##r", "##e"], ["##c", "##e"], ["##l", "##a"], ["##s", "##e"], ["##e", "##f"], ["a", "##u"], ["##r", "##a"], ["##t", "##ur"], ["re", "##tur"], ["retur", "##n"], ["p", "##ro"], ["T", "##he"], ["fu", "##nction"], ["d", "##ef"], ["##he", "##n"], ["##te", "##g"], ["##d", "##i"], ["##l", "##ic"], ["##al", "##i"], ["##ce", "##ss"], ["f", "##or"], ["##m", "##ent"], ["##ec", "##tion"], ["##h", "##er"], ["N", "##G"], ["##m", "##b"], ["##t", "##er"], ["a", "##s"], ["n", "##e"], ["t", "##e"], ["au", "##t"], ["##su", "##re"], ["pro", "##cess"], ["##te", "##m"], ["##lic", "##ation"], ["##ti", "##m"], ["op", "##tim"], ["e", "##l"], ["el", "##se"], ["##l", "##y"], ["##a", "##r"], ["i", "##mp"], ["t", "##o"], ["c", "##a"], ["##t", "##or"], ["##it", "##h"], ["##a", "##s"], ["##or", "##y"], ["##n", "##e"], ["p", "##ar"], ["##la", "##ss"], ["##e", "##nce"], ["##k", "##ing"], ["##at", "##ter"], ["##atter", "##n"], ["con", "##s"], ["##h", "##i"], ["in", "##teg"], ["co", "##mp"], ["##l", "##i"], ["##r", "##r"], ["##rr", "##or"], ["##u", "##n"], ["re", "##f"], ["i", "##tem"], ["##z", "##y"], ["##z", "##zy"], ["fu", "##zzy"], ["a", "##t"], ["##n", "##tion"], ["##te", "##ntion"], ["at", "##tention"], ["##y", "##nc"], ["as", "##ync"], ["##n", "##d"], ["##le", "##ment"], ["i", "##f"], ["##o", "##s"], ["##os", "##ition"], ["a", "##l"], ["m", "##o"], ["##g", "##or"], ["##d", "##al"], ["##ith", "##m"], ["al", "##gor"], ["mo", "##dal"], ["algor", "##ithm"], ["##f", "##i"], ["b", "##e"], ["##r", "##y"], ["##n", "##s"], ["e", "##nc"], ["##a", "##te"], ["##or", "##al"], ["##mp", "##oral"], ["te", "##mporal"], ["b", "##ic"], ["p", "##attern"], ["##d", "##ition"], ["##on", "##dition"], ["optim", "##ization"], ["bic", "##ondition"], ["bicondition", "##al"], ["##ti", "##c"], ["##p", "##u"], ["##c", "##ur"], ["imp", "##lication"], ["##cur", "##s"], ["l", "##ist"], ["##g", "##ation"], ["ne", "##gation"], ["##u", "##nction"], ["##j", "##unction"], ["con", "##junction"], ["##e", "##l"], ["t", "##he"], ["E", "##N"], ["S", "##T"], ["##A", "##R"], ["EN", "##D"], ["ST", "##AR"], ["STAR", "##T"], ["d", "##at"], ["dat", "##a"], ["3", "##8"], ["##r", "##u"], ["##o", "##l"], ["##c", "##tor"], ["##l", "##o"], ["##a", "##i"], ["2", "##5"], ["fu", "##nc"], ["7", "##4"], ["4", "##9"], ["l", "##a"], ["##d", "##a"], ["##mb", "##da"], ["la", "##mbda"], ["4", "##2"], ["6", "##6"], ["##c", "##h"], ["##on", "##ing"], ["re", "##as"], ["reas", "##oning"], ["7", "##0"], ["3", "##2"], ["7", "##1"], ["c", "##lass"], ["##e", "##d"], ["5", "##6"], ["5", "##7"], ["7", "##5"], ["e", "##x"], ["7", "##7"], ["6", "##0"], ["7", "##9"], ["I", "##f"], ["t", "##hen"], ["3", "##0"], ["4", "##5"], ["w", "##hi"], ["whi", "##le"], ["7", "##3"], ["8", "##5"], ["h", "##a"], ["##nd", "##le"], ["ha", "##ndle"], ["4", "##8"], ["5", "##2"], ["a", "##g"], ["t", "##h"], ["ag", "##ent"], ["th", "##at"], ["5", "##3"], ["5", "##9"], ["6", "##8"], ["##i", "##ne"], ["8", "##0"], ["ne", "##ur"], ["4", "##0"], ["2", "##6"], ["4", "##3"], ["8", "##6"], ["3", "##3"], ["7", "##6"], ["8", "##3"], ["6", "##7"], ["8", "##1"], ["##c", "##ognition"], ["meta", "##cognition"], ["3", "##1"], ["3", "##6"], ["6", "##1"], ["6", "##3"], ["g", "##ra"], ["p", "##la"], ["##n", "##n"], ["##di", "##ent"], ["gra", "##dient"], ["pla", "##nn"], ["plann", "##er"], ["3", "##9"], ["5", "##5"], ["e", "##rror"], ["##r", "##king"], ["##h", "##ma"], ["##nc", "##hma"], ["be", "##nchma"], ["benchma", "##rking"], ["4", "##1"], ["7", "##8"], ["##teg", "##ory"], ["ca", "##tegory"], ["3", "##4"], ["4", "##6"], ["5", "##0"], ["##m", "##ory"], ["me", "##mory"], ["2", "##9"], ["2", "##7"], ["3", "##5"], ["6", "##9"], ["8", "##4"], ["d", "##ec"], ["u", "##s"], ["##o", "##her"], ["##l", "##ection"], ["##i", "##ta"], ["##ti", "##tion"], ["in", "##her"], ["par", "##tition"], ["ref", "##lection"], ["dec", "##oher"], ["us", "##ing"], ["##ita", "##nce"], ["inher", "##itance"], ["decoher", "##ence"], ["##n", "##ation"], ["##i", "##s"], ["##or", "##di"], ["##nc", "##y"], ["##te", "##ncy"], ["co", "##ordi"], ["cons", "##is"], ["coordi", "##nation"], ["consis", "##tency"], ["2", "##4"], ["4", "##7"], ["6", "##2"], ["s", "##um"], ["##l", "##ization"], ["##al", "##le"], ["par", "##alle"], ["paralle", "##lization"], ["8", "##2"], ["7", "##2"], ["##n", "##ti"], ["##de", "##nti"], ["con", "##fi"], ["##ali", "##ty"], ["##denti", "##ality"], ["confi", "##dentiality"], ["##r", "##al"], ["integ", "##ral"], ["2", "##8"], ["5", "##8"], ["s", "##ali"], ["sali", "##ence"], ["5", "##1"], ["W", "##hen"], ["l", "##o"], ["##o", "##p"], ["comp", "##osition"], ["lo", "##op"], ["6", "##5"], ["a", "##v"], ["##a", "##sure"], ["##i", "##li"], ["##p", "##lication"], ["##b", "##ili"], ["re", "##plication"], ["me", "##asure"], ["##la", "##bili"], ["##ai", "##labili"], ["av", "##a<PERSON><PERSON>i"], ["measure", "##ment"], ["availabili", "##ty"], ["c", "##h"], ["##un", "##king"], ["ch", "##unking"], ["4", "##4"], ["##d", "##it"], ["au", "##dit"], ["##hen", "##tic"], ["aut", "##hentic"], ["authentic", "##ation"], ["b", "##i"], ["g", "##ate"], ["##a", "##nce"], ["##o", "##u"], ["##c", "##i"], ["##s", "##ne"], ["##h", "##or"], ["aut", "##hor"], ["cons", "##ci"], ["comp", "##li"], ["bi", "##as"], ["##ou", "##sne"], ["author", "##ization"], ["consci", "##ousne"], ["compli", "##ance"], ["<PERSON><PERSON>", "##ss"], ["3", "##7"], ["6", "##4"], ["integ", "##rity"], ["c", "##or"], ["t", "##un"], ["##r", "##ection"], ["cor", "##rection"], ["tun", "##ing"], ["qu", "##ali"], ["te", "##ns"], ["quali", "##a"], ["tens", "##or"], ["c", "##lo"], ["##p", "##ol"], ["##og", "##y"], ["to", "##pol"], ["clo", "##sure"], ["topol", "##ogy"], ["s", "##c"], ["##i", "##on"], ["re", "##curs"], ["##al", "##ing"], ["sc", "##aling"], ["recurs", "##ion"], ["a", "##st"], ["v", "##ec"], ["##iz", "##e"], ["optim", "##ize"], ["##tor", "##ization"], ["vec", "##torization"], ["g", "##o"], ["##n", "##su"], ["##i", "##p"], ["##p", "##tion"], ["##ss", "##ip"], ["con", "##se"], ["##ry", "##ption"], ["enc", "##ryption"], ["go", "##ssip"], ["##nsu", "##s"], ["conse", "##nsus"], ["##a", "##ctor"], ["##r", "##og"], ["##p", "##rog"], ["##m", "##m"], ["meta", "##prog"], ["##ra", "##mm"], ["ref", "##actor"], ["metaprog", "##ramm"], ["metaprogramm", "##ing"], ["n", "##on"], ["##re", "##pu"], ["##di", "##ation"], ["non", "##repu"], ["nonrepu", "##diation"], ["5", "##4"], ["r", "##a"], ["t", "##ry"], ["##f", "##t"], ["##p", "##t"], ["##ce", "##pt"], ["ex", "##cept"], ["ra", "##ft"], ["i", "##nt"], ["s", "##u"], ["##s", "##p"], ["##p", "##er"], ["##p", "##osition"], ["##ro", "##sp"], ["int", "##rosp"], ["su", "##per"], ["introsp", "##ection"], ["super", "##position"], ["1", "##7"], ["e", "##mb"], ["##d", "##ing"], ["##ed", "##ding"], ["emb", "##edding"], ["C", "##lass"], ["neur", "##al"], ["ca", "##ch"], ["cach", "##ing"], ["e", "##nt"], ["##a", "##p"], ["##a", "##ng"], ["##sul", "##ation"], ["enc", "##ap"], ["ent", "##ang"], ["encap", "##sulation"], ["entang", "##lement"], ["1", "##5"], ["##l", "##ing"], ["pro", "##fi"], ["profi", "##ling"], ["##ri", "##x"], ["mat", "##rix"], ["p", "##o"], ["t", "##ra"], ["##s", "##m"], ["##p", "##hi"], ["##m", "##or"], ["##m", "##er"], ["##for", "##mer"], ["con", "##st"], ["##ly", "##mor"], ["##ns", "##former"], ["po", "##lymor"], ["tra", "##nsformer"], ["##phi", "##sm"], ["polymor", "##phism"], ["a", "##b"], ["s", "##el"], ["##c", "##tion"], ["##st", "##ra"], ["ab", "##stra"], ["sel", "##f"], ["abstra", "##ction"], ["D", "##ef"], ["Def", "##ine"], ["C", "##re"], ["in", "##pu"], ["Cre", "##ate"], ["inpu", "##t"], ["A", "##p"], ["##t", "##ter"], ["##p", "##ly"], ["be", "##tter"], ["Ap", "##ply"], ["I", "##mp"], ["a", "##nd"], ["Imp", "##lement"], ["1", "##4"], ["1", "##6"], ["1", "##9"], ["8", "##7"], ["##i", "##el"], ["##iel", "##d"], ["1", "##3"], ["e", "##n"], ["##sure", "##s"], ["en", "##sures"], ["2", "##0"], ["o", "##c"], ["s", "##y"], ["##o", "##ma"], ["##s", "##tem"], ["##al", "##ly"], ["aut", "##oma"], ["##tic", "##ally"], ["oc", "##curs"], ["sy", "##stem"], ["automa", "##tically"], ["##ro", "##m"], ["u", "##se"], ["##t", "##ru"], ["##c", "##t"], ["use", "##s"], ["##tru", "##ct"], ["1", "##8"], ["return", "##s"], ["1", "##2"], ["##or", "##t"], ["imp", "##ort"], ["2", "##1"], ["1", "##0"], ["E", "##rror"], ["R", "##e"], ["a", "##w"], ["f", "##n"], ["##a", "##it"], ["Re", "##sult"], ["aw", "##ait"], ["in", "##it"], ["l", "##e"], ["o", "##f"], ["le", "##t"], ["S", "##i"], ["t", "##her"], ["##e", "##for"], ["Si", "##nce"], ["ther", "##efor"], ["therefor", "##e"], ["1", "##1"], ["v", "##ar"], ["##ru", "##ctor"], ["const", "##ructor"], ["S", "##y"], ["##o", "##lic"], ["##mb", "##olic"], ["Sy", "##mbolic"], ["P", "##attern"], ["v", "##al"], ["y", "##ield"], ["##a", "##u"], ["##l", "##t"], ["##u", "##e"], ["mat", "##ch"], ["def", "##au"], ["val", "##ue"], ["defau", "##lt"], ["N", "##o"], ["No", "##t"], ["##E", "##rror"], ["F", "##rom"], ["f", "##ol"], ["i", "##t"], ["##w", "##s"], ["##lo", "##ws"], ["fol", "##lows"], ["E", "##it"], ["w", "##ith"], ["##at", "##or"], ["co", "##mb"], ["oper", "##ator"], ["##ro", "##cess"], ["##ine", "##s"], ["Eit", "##her"], ["comb", "##ines"], ["##rocess", "##or"], ["G", "##i"], ["d", "##ed"], ["w", "##e"], ["##e", "##n"], ["##u", "##ce"], ["##v", "##en"], ["ca", "##n"], ["Gi", "##ven"], ["ded", "##uce"], ["T", "##ru"], ["##ec", "##ute"], ["ex", "##ecute"], ["Tru", "##e"], ["S", "##truct"], ["f", "##ield"], ["i", "##3"], ["s", "##truct"], ["imp", "##lement"], ["i3", "##2"], ["implement", "##s"], ["2", "##2"], ["f", "##rom"], ["m", "##ai"], ["##P", "##rocessor"], ["mai", "##n"], ["B", "##e"], ["##g", "##i"], ["##p", "##h"], ["##og", "##ly"], ["neur", "##ogly"], ["Be", "##gi"], ["neurogly", "##ph"], ["<PERSON><PERSON>", "##n"], ["P", "##rocessor"], ["78", "##ng"], ["56", "##ng"], ["68", "##ng"], ["32", "##ng"], ["75", "##ng"], ["82", "##ng"], ["48", "##ng"], ["86", "##ng"], ["83", "##ng"], ["16", "##ng"], ["25", "##ng"], ["70", "##ng"], ["57", "##ng"], ["30", "##ng"], ["80", "##ng"], ["31", "##ng"], ["36", "##ng"], ["55", "##ng"], ["41", "##ng"], ["51", "##ng"], ["20", "##ng"], ["21", "##ng"], ["74", "##ng"], ["52", "##ng"], ["59", "##ng"], ["43", "##ng"], ["76", "##ng"], ["34", "##ng"], ["29", "##ng"], ["35", "##ng"], ["58", "##ng"], ["17", "##ng"], ["87", "##ng"], ["49", "##Error"], ["42", "##ng"], ["77", "##ng"], ["79", "##ng"], ["45", "##ng"], ["45", "##Processor"], ["73", "##ng"], ["85", "##ng"], ["26", "##ng"], ["33", "##ng"], ["81", "##ng"], ["63", "##ng"], ["41", "##Error"], ["24", "##ng"], ["62", "##ng"], ["72", "##ng"], ["65", "##ng"], ["44", "##ng"], ["64", "##ng"], ["15", "##ng"], ["2", "##3"], ["6", "##Error"], ["7", "##ng"], ["8", "##ng"], ["38", "##ng"], ["49", "##ng"], ["42", "##Processor"], ["71", "##ng"], ["75", "##Error"], ["30", "##Error"], ["53", "##ng"], ["40", "##ng"], ["83", "##Processor"], ["61", "##ng"], ["39", "##ng"], ["50", "##ng"], ["29", "##Error"], ["27", "##ng"], ["69", "##ng"], ["47", "##ng"], ["47", "##Error"], ["62", "##Error"], ["62", "##Processor"], ["82", "##Error"], ["28", "##ng"], ["28", "##Error"], ["44", "##Error"], ["37", "##ng"], ["17", "##Error"], ["14", "##Processor"], ["19", "##Error"], ["13", "##ng"], ["11", "##ng"], ["1", "##ng"], ["1", "##Error"], ["3", "##ng"], ["4", "##ng"], ["5", "##ng"], ["6", "##ng"], ["8", "##Processor"], ["9", "##ng"], ["ℛ", "##ng"], ["↨", "##⎼"], ["≳", "##⍊"], ["⊖", "##⋌"], ["⋎", "##⧏"], ["⋖", "##⬬"], ["⌍", "##⊃"], ["⎘", "##⧎"], ["⏅", "##⧜"], ["⏜", "##⮩"], ["⏡", "##⫨"], ["⦾", "##🞳"], ["⯌", "##🛇"], ["ꞏ", "##ng"], ["𝐋", "##Processor"], ["🢃", "##⭻"], ["##𝟬", "##ng"], ["##⇊", "##⅐"], ["##Ⅴ", "##ng"], ["##Ⅳ", "##ng"], ["##𝚎", "##ng"], ["38", "##Processor"], ["49", "##Processor"], ["42", "##Ⅳng"], ["66", "##ng"], ["66", "##Processor"], ["70", "##Error"], ["70", "##Processor"], ["56", "##Error"], ["57", "##Error"], ["57", "##Processor"], ["60", "##ng"], ["60", "##Error"], ["60", "##Processor"], ["79", "##Processor"], ["30", "##Processor"], ["73", "##Processor"], ["48", "##ⅴ"], ["48", "##𝟬ng"], ["52", "##Error"], ["53", "##Error"], ["59", "##Error"], ["68", "##Error"], ["68", "##Processor"], ["80", "##Processor"], ["26", "##Processor"], ["43", "##Error"], ["83", "##Error"], ["31", "##Processor"], ["36", "##Processor"], ["61", "##Processor"], ["63", "##Error"], ["63", "##Processor"], ["39", "##Processor"], ["55", "##Error"], ["78", "##Error"], ["78", "##Processor"], ["46", "##ng"], ["46", "##Error"], ["46", "##Processor"], ["27", "##Error"], ["27", "##Processor"], ["35", "##Processor"], ["69", "##Error"], ["69", "##Processor"], ["82", "##𝚶"], ["58", "##Error"], ["51", "##𝑜"], ["51", "##Processor"], ["44", "##Processor"], ["64", "##Error"], ["54", "##ng"], ["54", "##Processor"], ["15", "##Ⅴng"], ["14", "##ng"], ["19", "##ng"], ["13", "##Error"], ["20", "##Processor"], ["18", "##ng"], ["18", "##Error"], ["21", "##Error"], ["10", "##ng"], ["10", "##𝚎ng"], ["11", "##Error"], ["⯌🛇", "##⇊⅐"]]}}