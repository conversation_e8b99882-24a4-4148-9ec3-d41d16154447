#!/usr/bin/env python3
"""
Verificatore Zero-Splitting NEUROGLYPH v2.0

Sistema completo di verifica per garantire:
1. Zero-splitting per tutti i neuroglifi
2. Roundtrip fidelity perfetta
3. Coerenza tokenizzazione/detokenizzazione
4. Test di sicurezza automatici
5. Rilevamento anomalie e correzioni
"""

import json
import random
from pathlib import Path
from typing import Dict, List, Any, Set, Tuple
from transformers import PreTrainedTokenizerFast
import re
from collections import defaultdict

class ZeroSplittingVerifier:
    """Verificatore completo per zero-splitting."""
    
    def __init__(self, 
                 tokenizer_path: str = "training/tokenizer/neuroglyph_tokenizer",
                 registry_path: str = "neuroglyph_ULTIMATE_registry.json"):
        """
        Inizializza il verificatore.
        
        Args:
            tokenizer_path: Percorso al tokenizer
            registry_path: Percorso al registry simbolico
        """
        self.tokenizer_path = tokenizer_path
        self.registry_path = registry_path
        self.symbols = []
        self.symbol_by_domain = {}
        self._load_symbols()
        
        # Carica tokenizer
        if Path(tokenizer_path).exists():
            self.tokenizer = PreTrainedTokenizerFast.from_pretrained(tokenizer_path)
            print(f"✅ Tokenizer caricato da: {tokenizer_path}")
        else:
            self.tokenizer = None
            print(f"❌ Tokenizer non trovato: {tokenizer_path}")
    
    def _load_symbols(self) -> None:
        """Carica simboli dal registry."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                registry = json.load(f)
            
            approved_symbols = registry.get('approved_symbols', [])
            
            for symbol_data in approved_symbols:
                symbol = symbol_data.get('symbol')
                domain = symbol_data.get('domain', 'general')
                
                if symbol:
                    self.symbols.append(symbol)
                    
                    if domain not in self.symbol_by_domain:
                        self.symbol_by_domain[domain] = []
                    self.symbol_by_domain[domain].append(symbol)
            
            print(f"✅ Caricati {len(self.symbols)} simboli da {len(self.symbol_by_domain)} domini")
            
        except Exception as e:
            print(f"❌ Errore caricamento simboli: {e}")
            self.symbols = []
    
    def generate_test_phrases(self, num_phrases: int = 100) -> List[str]:
        """Genera frasi-tipo per test con neuroglifi mappati."""
        print(f"🔧 Generazione {num_phrases} frasi-tipo per test...")
        
        # Template di frasi con placeholder per simboli
        phrase_templates = [
            # Codice con simboli
            "def {symbol}_function({symbol}, {symbol}): return {symbol}",
            "class {symbol}Processor: def {symbol}(self): return {symbol}",
            "if {symbol} == {symbol}: {symbol}() else {symbol}()",
            "for {symbol} in {symbol}_list: process({symbol})",
            "while {symbol} > 0: {symbol} -= 1",
            "try: {symbol}() except {symbol}Error: {symbol}()",
            
            # Logica simbolica
            "{symbol} ⊢ {symbol}",
            "{symbol} ∧ {symbol} → {symbol}",
            "If {symbol} then {symbol} else {symbol}",
            "Given {symbol}, we deduce {symbol}",
            "{symbol} ↔ {symbol}",
            "¬{symbol} ∨ {symbol}",
            
            # Testo naturale con simboli
            "The function {symbol} processes {symbol} data",
            "When {symbol} occurs, execute {symbol}",
            "The {symbol} algorithm uses {symbol} optimization",
            "Create a {symbol} that handles {symbol}",
            "The {symbol} operator combines {symbol} with {symbol}",
            
            # Sequenze pure di simboli
            "{symbol} {symbol} {symbol}",
            "{symbol} → {symbol} → {symbol}",
            "({symbol}, {symbol}, {symbol})",
            "[{symbol}, {symbol}, {symbol}]",
            "{{{symbol}, {symbol}, {symbol}}}",
            
            # Markup NEUROGLYPH
            "<NG_START> {symbol} {symbol} <NG_END>",
            "<NG_THINK> {symbol} ⊢ {symbol} </NG_THINK>",
            "<NG_REASON> {symbol} ∧ {symbol} → {symbol} </NG_REASON>",
            
            # Misti complessi
            "def {symbol}(): return {symbol}({symbol}) if {symbol} else {symbol}",
            "lambda {symbol}: {symbol}({symbol}) + {symbol}",
            "async def {symbol}(): await {symbol}(); return {symbol}",
            "{symbol}.{symbol}({symbol}).{symbol}({symbol})",
            "import {symbol}; from {symbol} import {symbol}"
        ]
        
        test_phrases = []
        
        for _ in range(num_phrases):
            template = random.choice(phrase_templates)
            
            # Sostituisci placeholder con simboli casuali
            phrase = template
            while '{symbol}' in phrase:
                symbol = random.choice(self.symbols)
                phrase = phrase.replace('{symbol}', symbol, 1)
            
            test_phrases.append(phrase)
        
        # Aggiungi frasi specifiche per test critici
        critical_phrases = [
            # Simboli singoli
            *random.choices(self.symbols, k=20),
            
            # Coppie di simboli
            *[f"{s1} {s2}" for s1, s2 in zip(
                random.choices(self.symbols, k=10),
                random.choices(self.symbols, k=10)
            )],
            
            # Simboli con punteggiatura
            *[f"{s}," for s in random.choices(self.symbols, k=5)],
            *[f"({s})" for s in random.choices(self.symbols, k=5)],
            *[f'"{s}"' for s in random.choices(self.symbols, k=5)],
            
            # Simboli con spazi
            *[f" {s} " for s in random.choices(self.symbols, k=5)],
            *[f"\t{s}\n" for s in random.choices(self.symbols, k=5)]
        ]
        
        test_phrases.extend(critical_phrases)
        
        print(f"✅ Generate {len(test_phrases)} frasi-tipo")
        return test_phrases
    
    def test_individual_symbols(self) -> Dict[str, Any]:
        """Testa ogni simbolo individualmente per zero-splitting."""
        print("🔍 Test zero-splitting simboli individuali...")
        
        if not self.tokenizer:
            return {"error": "Tokenizer non disponibile"}
        
        results = {
            'total_symbols': len(self.symbols),
            'zero_split_symbols': 0,
            'split_symbols': [],
            'missing_symbols': [],
            'perfect_symbols': [],
            'zero_split_percentage': 0.0
        }
        
        for symbol in self.symbols:
            # Test tokenizzazione
            tokens = self.tokenizer.tokenize(symbol)
            token_ids = self.tokenizer.encode(symbol, add_special_tokens=False)
            
            # Verifica presenza nel vocabolario
            if symbol not in self.tokenizer.get_vocab():
                results['missing_symbols'].append(symbol)
                continue
            
            # Verifica zero-splitting
            if len(tokens) == 1 and tokens[0] == symbol:
                results['zero_split_symbols'] += 1
                results['perfect_symbols'].append(symbol)
            else:
                results['split_symbols'].append({
                    'symbol': symbol,
                    'tokens': tokens,
                    'token_count': len(tokens),
                    'token_ids': token_ids
                })
        
        results['zero_split_percentage'] = (results['zero_split_symbols'] / results['total_symbols']) * 100
        
        print(f"📊 Risultati Test Simboli Individuali:")
        print(f"   - Simboli testati: {results['total_symbols']}")
        print(f"   - Zero-splitting perfetto: {results['zero_split_symbols']} ({results['zero_split_percentage']:.1f}%)")
        print(f"   - Simboli divisi: {len(results['split_symbols'])}")
        print(f"   - Simboli mancanti: {len(results['missing_symbols'])}")
        
        return results
    
    def test_roundtrip_fidelity(self, test_phrases: List[str]) -> Dict[str, Any]:
        """Testa la fedeltà roundtrip per le frasi."""
        print("🔍 Test roundtrip fidelity...")
        
        if not self.tokenizer:
            return {"error": "Tokenizer non disponibile"}
        
        results = {
            'total_phrases': len(test_phrases),
            'perfect_roundtrips': 0,
            'failed_roundtrips': [],
            'roundtrip_percentage': 0.0,
            'common_issues': defaultdict(int)
        }
        
        for phrase in test_phrases:
            # Test con e senza special tokens
            for add_special in [False, True]:
                token_ids = self.tokenizer.encode(phrase, add_special_tokens=add_special)
                decoded = self.tokenizer.decode(token_ids, skip_special_tokens=not add_special)
                
                # Normalizza spazi per confronto
                original_normalized = ' '.join(phrase.split())
                decoded_normalized = ' '.join(decoded.split())
                
                if original_normalized == decoded_normalized:
                    results['perfect_roundtrips'] += 1
                    break  # Almeno una versione funziona
            else:
                # Nessuna versione ha funzionato
                tokens = self.tokenizer.tokenize(phrase)
                token_ids_no_special = self.tokenizer.encode(phrase, add_special_tokens=False)
                decoded_no_special = self.tokenizer.decode(token_ids_no_special, skip_special_tokens=False)
                
                results['failed_roundtrips'].append({
                    'original': phrase,
                    'tokens': tokens,
                    'decoded': decoded_no_special,
                    'length_diff': len(phrase) - len(decoded_no_special)
                })
                
                # Analizza tipo di problema
                if '<UNK>' in tokens:
                    results['common_issues']['unknown_tokens'] += 1
                if len(tokens) > len(phrase.split()) * 2:
                    results['common_issues']['over_tokenization'] += 1
                if any('##' in token for token in tokens):
                    results['common_issues']['subword_splitting'] += 1
        
        results['roundtrip_percentage'] = (results['perfect_roundtrips'] / results['total_phrases']) * 100
        
        print(f"📊 Risultati Test Roundtrip:")
        print(f"   - Frasi testate: {results['total_phrases']}")
        print(f"   - Roundtrip perfetti: {results['perfect_roundtrips']} ({results['roundtrip_percentage']:.1f}%)")
        print(f"   - Roundtrip falliti: {len(results['failed_roundtrips'])}")
        
        if results['common_issues']:
            print(f"   - Problemi comuni:")
            for issue, count in results['common_issues'].items():
                print(f"     {issue}: {count}")
        
        return results
    
    def test_security_random_samples(self, num_samples: int = 100) -> Dict[str, Any]:
        """Test di sicurezza con campioni casuali."""
        print(f"🔍 Test di sicurezza con {num_samples} campioni casuali...")
        
        if not self.tokenizer:
            return {"error": "Tokenizer non disponibile"}
        
        # Genera campioni casuali
        random_samples = []
        
        for _ in range(num_samples):
            # Lunghezza casuale
            length = random.randint(1, 10)
            
            # Mix di simboli e testo
            components = []
            for _ in range(length):
                if random.random() < 0.6:  # 60% simboli
                    components.append(random.choice(self.symbols))
                else:  # 40% testo normale
                    components.append(random.choice([
                        'function', 'class', 'if', 'then', 'else', 'for', 'while',
                        'return', 'def', 'import', 'from', 'as', 'try', 'except',
                        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at'
                    ]))
            
            sample = ' '.join(components)
            random_samples.append(sample)
        
        # Test equivalenza
        results = {
            'total_samples': num_samples,
            'perfect_equivalence': 0,
            'failed_equivalence': [],
            'equivalence_percentage': 0.0,
            'anomalies': []
        }
        
        for sample in random_samples:
            try:
                # Tokenizza e detokenizza
                token_ids = self.tokenizer.encode(sample, add_special_tokens=False)
                decoded = self.tokenizer.decode(token_ids, skip_special_tokens=False)
                
                # Normalizza per confronto
                original_norm = ' '.join(sample.split())
                decoded_norm = ' '.join(decoded.split())
                
                if original_norm == decoded_norm:
                    results['perfect_equivalence'] += 1
                else:
                    results['failed_equivalence'].append({
                        'original': sample,
                        'decoded': decoded,
                        'tokens': self.tokenizer.tokenize(sample)
                    })
                
                # Rileva anomalie
                tokens = self.tokenizer.tokenize(sample)
                if len(tokens) > len(sample) * 2:  # Troppi token
                    results['anomalies'].append({
                        'type': 'over_tokenization',
                        'sample': sample,
                        'token_count': len(tokens),
                        'char_count': len(sample)
                    })
                
                if any(len(token) > 20 for token in tokens):  # Token troppo lunghi
                    results['anomalies'].append({
                        'type': 'long_tokens',
                        'sample': sample,
                        'long_tokens': [t for t in tokens if len(t) > 20]
                    })
                
            except Exception as e:
                results['anomalies'].append({
                    'type': 'encoding_error',
                    'sample': sample,
                    'error': str(e)
                })
        
        results['equivalence_percentage'] = (results['perfect_equivalence'] / results['total_samples']) * 100
        
        print(f"📊 Risultati Test Sicurezza:")
        print(f"   - Campioni testati: {results['total_samples']}")
        print(f"   - Equivalenza perfetta: {results['perfect_equivalence']} ({results['equivalence_percentage']:.1f}%)")
        print(f"   - Equivalenza fallita: {len(results['failed_equivalence'])}")
        print(f"   - Anomalie rilevate: {len(results['anomalies'])}")
        
        return results
    
    def analyze_tokenization_patterns(self, test_phrases: List[str]) -> Dict[str, Any]:
        """Analizza pattern di tokenizzazione per identificare problemi."""
        print("🔍 Analisi pattern di tokenizzazione...")
        
        if not self.tokenizer:
            return {"error": "Tokenizer non disponibile"}
        
        analysis = {
            'symbol_usage': defaultdict(int),
            'token_length_distribution': defaultdict(int),
            'subword_patterns': defaultdict(int),
            'unknown_token_contexts': [],
            'splitting_patterns': defaultdict(list)
        }
        
        for phrase in test_phrases[:50]:  # Analizza primi 50 per performance
            tokens = self.tokenizer.tokenize(phrase)
            
            for token in tokens:
                # Conta uso simboli
                if token in self.symbols:
                    analysis['symbol_usage'][token] += 1
                
                # Distribuzione lunghezza token
                analysis['token_length_distribution'][len(token)] += 1
                
                # Pattern subword
                if token.startswith('##'):
                    analysis['subword_patterns']['subword_prefix'] += 1
                
                # Token sconosciuti
                if token == '<UNK>' or token == '[UNK]':
                    analysis['unknown_token_contexts'].append({
                        'phrase': phrase,
                        'tokens': tokens
                    })
            
            # Analizza splitting di simboli
            for symbol in self.symbols:
                if symbol in phrase:
                    symbol_tokens = []
                    phrase_tokens = self.tokenizer.tokenize(phrase)
                    
                    # Trova come il simbolo è stato tokenizzato
                    for i, token in enumerate(phrase_tokens):
                        if symbol in token or token in symbol:
                            symbol_tokens.append(token)
                    
                    if len(symbol_tokens) > 1:
                        analysis['splitting_patterns'][symbol].extend(symbol_tokens)
        
        print(f"📊 Analisi Pattern:")
        print(f"   - Simboli usati: {len(analysis['symbol_usage'])}")
        print(f"   - Token sconosciuti: {len(analysis['unknown_token_contexts'])}")
        print(f"   - Simboli divisi: {len(analysis['splitting_patterns'])}")
        
        return analysis
    
    def generate_comprehensive_report(self, 
                                    symbol_results: Dict[str, Any],
                                    roundtrip_results: Dict[str, Any],
                                    security_results: Dict[str, Any],
                                    pattern_analysis: Dict[str, Any]) -> str:
        """Genera report completo di verifica."""
        report = []
        report.append("# NEUROGLYPH Zero-Splitting Verification Report")
        report.append("=" * 70)
        report.append("")
        
        # Sommario esecutivo
        zero_split_pct = symbol_results.get('zero_split_percentage', 0)
        roundtrip_pct = roundtrip_results.get('roundtrip_percentage', 0)
        security_pct = security_results.get('equivalence_percentage', 0)
        
        report.append("## 📊 SOMMARIO ESECUTIVO")
        report.append(f"- **Zero-Splitting**: {zero_split_pct:.1f}% ({symbol_results.get('zero_split_symbols', 0)}/{symbol_results.get('total_symbols', 0)} simboli)")
        report.append(f"- **Roundtrip Fidelity**: {roundtrip_pct:.1f}% ({roundtrip_results.get('perfect_roundtrips', 0)}/{roundtrip_results.get('total_phrases', 0)} frasi)")
        report.append(f"- **Security Test**: {security_pct:.1f}% ({security_results.get('perfect_equivalence', 0)}/{security_results.get('total_samples', 0)} campioni)")
        report.append("")
        
        # Valutazione complessiva
        overall_score = (zero_split_pct + roundtrip_pct + security_pct) / 3
        
        if overall_score >= 95:
            status = "✅ ECCELLENTE"
        elif overall_score >= 85:
            status = "✅ BUONO"
        elif overall_score >= 70:
            status = "⚠️ ACCETTABILE"
        else:
            status = "❌ NECESSITA MIGLIORAMENTI"
        
        report.append(f"## 🎯 VALUTAZIONE COMPLESSIVA: {status}")
        report.append(f"**Punteggio Medio**: {overall_score:.1f}/100")
        report.append("")
        
        # Problemi critici
        if symbol_results.get('split_symbols'):
            report.append("## ❌ SIMBOLI CON SPLITTING (Critici)")
            for item in symbol_results['split_symbols'][:10]:
                report.append(f"- `{item['symbol']}` → {item['tokens']} ({item['token_count']} token)")
            report.append("")
        
        if roundtrip_results.get('failed_roundtrips'):
            report.append("## ⚠️ ROUNDTRIP FALLITI")
            for item in roundtrip_results['failed_roundtrips'][:5]:
                report.append(f"- Original: `{item['original']}`")
                report.append(f"  Decoded: `{item['decoded']}`")
                report.append("")
        
        # Raccomandazioni
        report.append("## 🔧 RACCOMANDAZIONI")
        
        if zero_split_pct < 95:
            report.append("1. **Migliorare Zero-Splitting**:")
            report.append("   - Aggiungere simboli problematici come token speciali")
            report.append("   - Modificare pre-tokenizer per preservare simboli")
            report.append("   - Aumentare frequenza simboli nel corpus")
        
        if roundtrip_pct < 90:
            report.append("2. **Migliorare Roundtrip Fidelity**:")
            report.append("   - Verificare gestione spazi e punteggiatura")
            report.append("   - Ottimizzare post-processor")
            report.append("   - Testare con skip_special_tokens=False")
        
        if security_pct < 95:
            report.append("3. **Migliorare Sicurezza**:")
            report.append("   - Gestire meglio casi edge")
            report.append("   - Ridurre token <UNK>")
            report.append("   - Validare encoding/decoding")
        
        report.append("")
        
        return "\n".join(report)

def main():
    """Funzione principale di verifica."""
    print("🧠 NEUROGLYPH v2.0 - Zero-Splitting Verifier")
    print("=" * 70)
    
    # Inizializza verificatore
    verifier = ZeroSplittingVerifier()
    
    if not verifier.tokenizer:
        print("❌ Tokenizer non disponibile. Esegui prima il training.")
        return False
    
    # 2.3.1 Genera frasi-tipo
    test_phrases = verifier.generate_test_phrases(100)
    
    # 2.3.2 Test zero-splitting simboli individuali
    symbol_results = verifier.test_individual_symbols()
    
    # 2.3.3 Test roundtrip fidelity
    roundtrip_results = verifier.test_roundtrip_fidelity(test_phrases)
    
    # 2.3.4 Test di sicurezza automatici
    security_results = verifier.test_security_random_samples(100)
    
    # 2.3.5 Analisi pattern per identificare anomalie
    pattern_analysis = verifier.analyze_tokenization_patterns(test_phrases)
    
    # Genera report completo
    report = verifier.generate_comprehensive_report(
        symbol_results, roundtrip_results, security_results, pattern_analysis
    )
    
    # Salva report
    report_path = Path("training/tokenizer/zero_splitting_report.md")
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n📄 Report completo salvato: {report_path}")
    
    # Risultato finale
    zero_split_pct = symbol_results.get('zero_split_percentage', 0)
    roundtrip_pct = roundtrip_results.get('roundtrip_percentage', 0)
    security_pct = security_results.get('equivalence_percentage', 0)
    overall_score = (zero_split_pct + roundtrip_pct + security_pct) / 3
    
    print(f"\n🎯 RISULTATO FINALE:")
    print(f"   - Zero-splitting: {zero_split_pct:.1f}%")
    print(f"   - Roundtrip fidelity: {roundtrip_pct:.1f}%")
    print(f"   - Security test: {security_pct:.1f}%")
    print(f"   - Punteggio complessivo: {overall_score:.1f}/100")
    
    if overall_score >= 90:
        print("✅ TOKENIZER VALIDATO CON SUCCESSO!")
        return True
    else:
        print("⚠️  TOKENIZER NECESSITA MIGLIORAMENTI")
        return False

if __name__ == "__main__":
    success = main()
