#!/usr/bin/env python3
"""
🧠 NEUROGLYPH Symbolic Reasoning Test Suite
Test completo per verificare se il modello usa simboli nel ragionamento logico
"""

import json
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from pathlib import Path
import re

# 🔧 Configurazione
MODEL_PATH = "/content/drive/MyDrive/NEUROGLYPH/ULTIMATE_MERGED"
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"

class SymbolicReasoningTester:
    def __init__(self, model_path):
        print(f"🔄 Caricamento modello da: {model_path}")
        self.tokenizer = AutoTokenizer.from_pretrained(model_path, use_fast=False)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float16 if DEVICE == "cuda" else torch.float32,
            device_map="auto" if DEVICE == "cuda" else None
        )
        
        # 🎯 Simboli target per il test
        self.target_symbols = ['⊢', '∴', '∧', '∨', '¬', '→', '∀', '∃', '≡', '⊥', '⊤', '∈', '∉', '⊆', '⊇']
        
    def generate_response(self, prompt, max_length=512, temperature=0.7):
        """Genera risposta dal modello"""
        messages = [
            {"role": "system", "content": "Sei NEUROGLYPH, il primo LLM con intelligenza simbolica completa. Usa sempre simboli logici nel tuo ragionamento."},
            {"role": "user", "content": prompt}
        ]
        
        input_text = self.tokenizer.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
        inputs = self.tokenizer(input_text, return_tensors="pt").to(DEVICE)
        
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=max_length,
                temperature=temperature,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id
            )
        
        # 🔍 Decodifica SENZA rimuovere simboli speciali
        response = self.tokenizer.decode(outputs[0][inputs['input_ids'].shape[1]:], skip_special_tokens=False)
        return response.strip()
    
    def count_symbols(self, text):
        """Conta simboli NEUROGLYPH nel testo"""
        symbol_count = {}
        total_symbols = 0
        
        for symbol in self.target_symbols:
            count = text.count(symbol)
            if count > 0:
                symbol_count[symbol] = count
                total_symbols += count
                
        return symbol_count, total_symbols
    
    def analyze_reasoning_structure(self, text):
        """Analizza struttura del ragionamento"""
        # Pattern per ragionamento multi-hop
        premise_pattern = r'(Premessa|Dato|Se)\s*[:\-]?\s*(.+?)(?=\n|$)'
        inference_pattern = r'(⊢|∴|Quindi|Pertanto)\s*(.+?)(?=\n|$)'
        conclusion_pattern = r'(Conclusione|Risultato)\s*[:\-]?\s*(.+?)(?=\n|$)'
        
        premises = re.findall(premise_pattern, text, re.IGNORECASE)
        inferences = re.findall(inference_pattern, text, re.IGNORECASE)
        conclusions = re.findall(conclusion_pattern, text, re.IGNORECASE)
        
        return {
            'premises': len(premises),
            'inferences': len(inferences),
            'conclusions': len(conclusions),
            'has_logical_structure': len(inferences) > 0 or len(conclusions) > 0
        }

def run_reasoning_tests():
    """Esegue batteria completa di test di ragionamento"""
    
    # 🧪 Test Cases per ragionamento simbolico
    test_cases = [
        {
            "name": "Modus Ponens Basico",
            "prompt": "Se P allora Q. P è vero. Cosa puoi concludere? Usa simboli logici nel ragionamento.",
            "expected_symbols": ['→', '⊢', '∴'],
            "reasoning_type": "deduttivo"
        },
        {
            "name": "Sillogismo Categorico", 
            "prompt": "Tutti gli uomini sono mortali. Socrate è un uomo. Cosa puoi dedurre? Mostra il ragionamento con simboli.",
            "expected_symbols": ['∀', '→', '⊢', '∴'],
            "reasoning_type": "universale"
        },
        {
            "name": "Congiunzione Logica",
            "prompt": "A è vero e B è vero. Cosa puoi dire di (A ∧ B)? Dimostra simbolicamente.",
            "expected_symbols": ['∧', '⊢', '⊤'],
            "reasoning_type": "proposizionale"
        },
        {
            "name": "Negazione e Contraddizione",
            "prompt": "Se P è falso, cosa puoi dire di ¬P? E se avessi sia P che ¬P?",
            "expected_symbols": ['¬', '⊥', '⊢'],
            "reasoning_type": "contraddizione"
        },
        {
            "name": "Ragionamento Esistenziale",
            "prompt": "Esiste almeno un numero primo pari. Come lo esprimi simbolicamente e cosa implica?",
            "expected_symbols": ['∃', '∧', '⊢'],
            "reasoning_type": "esistenziale"
        }
    ]
    
    print("🧠 NEUROGLYPH - Test di Ragionamento Simbolico")
    print("=" * 60)
    
    tester = SymbolicReasoningTester(MODEL_PATH)
    results = []
    
    for i, test in enumerate(test_cases, 1):
        print(f"\n🔬 Test {i}: {test['name']}")
        print(f"📝 Prompt: {test['prompt']}")
        print("-" * 40)
        
        # Genera risposta
        response = tester.generate_response(test['prompt'])
        print(f"🤖 Risposta: {response}")
        
        # Analizza simboli
        symbol_count, total_symbols = tester.count_symbols(response)
        print(f"🔢 Simboli trovati: {total_symbols} - {symbol_count}")
        
        # Analizza struttura ragionamento
        reasoning = tester.analyze_reasoning_structure(response)
        print(f"🧩 Struttura: {reasoning}")
        
        # Calcola score
        expected_found = sum(1 for sym in test['expected_symbols'] if sym in symbol_count)
        symbol_score = expected_found / len(test['expected_symbols']) if test['expected_symbols'] else 0
        
        result = {
            'test_name': test['name'],
            'response': response,
            'symbols_found': symbol_count,
            'total_symbols': total_symbols,
            'expected_symbols': test['expected_symbols'],
            'symbol_score': symbol_score,
            'reasoning_structure': reasoning,
            'has_logical_flow': reasoning['has_logical_structure']
        }
        results.append(result)
        
        print(f"📊 Score simbolico: {symbol_score:.2%}")
        print(f"🔗 Struttura logica: {'✅' if reasoning['has_logical_structure'] else '❌'}")
    
    # 📈 Riepilogo finale
    print("\n" + "=" * 60)
    print("📊 RIEPILOGO RISULTATI")
    print("=" * 60)
    
    total_tests = len(results)
    tests_with_symbols = sum(1 for r in results if r['total_symbols'] > 0)
    tests_with_structure = sum(1 for r in results if r['has_logical_flow'])
    avg_symbol_score = sum(r['symbol_score'] for r in results) / total_tests
    
    print(f"🧪 Test totali: {total_tests}")
    print(f"🔢 Test con simboli: {tests_with_symbols}/{total_tests} ({tests_with_symbols/total_tests:.1%})")
    print(f"🧩 Test con struttura logica: {tests_with_structure}/{total_tests} ({tests_with_structure/total_tests:.1%})")
    print(f"📊 Score simbolico medio: {avg_symbol_score:.1%}")
    
    # Verdetto finale
    if tests_with_symbols >= total_tests * 0.8 and avg_symbol_score >= 0.6:
        print("\n🎉 SUCCESSO: NEUROGLYPH usa simboli nel ragionamento!")
    elif tests_with_symbols >= total_tests * 0.5:
        print("\n⚠️ PARZIALE: Alcuni simboli presenti, serve ottimizzazione")
    else:
        print("\n❌ FALLIMENTO: Simboli assenti o insufficienti")
    
    return results

if __name__ == "__main__":
    results = run_reasoning_tests()
    
    # Salva risultati
    with open("symbolic_reasoning_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 Risultati salvati in: symbolic_reasoning_results.json")
